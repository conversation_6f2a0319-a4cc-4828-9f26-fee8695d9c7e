{"name": "svelte-preprocess", "version": "6.0.3", "license": "MIT", "main": "dist/index.js", "types": "dist/index.d.ts", "type": "commonjs", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "description": "A Svelte preprocessor wrapper with baked-in support for commonly used preprocessors", "author": "<PERSON> <<EMAIL>>", "repository": "https://github.com/sveltejs/svelte-preprocess", "keywords": ["svelte", "preprocess", "typescript", "less", "stylus", "sass", "scss", "pug", "coffeescript"], "engines": {"node": ">= 18.0.0"}, "packageManager": "pnpm@9.3.0", "volta": {"node": "20.10.0"}, "files": ["dist/"], "scripts": {"prebuild": "node scripts.js rmrf ./dist", "build": "tsc --build tsconfig.build.json", "dev": "pnpm build -w", "test": "vitest run", "lint": "eslint --ext js,ts .", "format": "prettier --write \"**/*.{ts,js,json}\"", "postinstall": "echo \"[svelte-preprocess] Don't forget to install the preprocessors packages that will be used: sass, stylus, less, postcss & postcss-load-config, coffeescript, pug, etc...\"", "version": "conventional-changelog -p angular -i CHANGELOG.md -s -r 1 && git add CHANGELOG.md", "patch": "npm version patch -m 'chore(release): %s'", "minor": "npm version minor -m 'chore(release): %s'", "major": "npm version major -m 'chore(release): %s'", "prepublishOnly": "pnpm test && pnpm build"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "devDependencies": {"@babel/core": "^7.23.6", "@babel/preset-env": "^7.23.6", "@commitlint/cli": "^11.0.0", "@commitlint/config-conventional": "^11.0.0", "@kiwi/eslint-config": "^2.0.2", "@kiwi/prettier-config": "^2.0.2", "@types/babel__core": "^7.20.5", "@types/node": "^18.0.0", "@types/pug": "^2.0.6", "@types/stylus": "^0.48.38", "autoprefixer": "^10.4.16", "babel-minify": "^0.5.2", "coffeescript": "^2.7.0", "conventional-changelog-cli": "^2.2.2", "eslint": "^8.29.0", "less": "^3.13.1", "postcss": "^8.4.32", "postcss-easy-import": "^4.0.0", "postcss-load-config": "^3.1.4", "prettier": "^3.3.2", "pug": "^3.0.2", "sass": "^1.79.3", "stylus": "^0.55.0", "sugarss": "^4.0.0", "svelte": "^4.0.0", "typescript": "^5.0.2", "vitest": "^1.6.0"}, "peerDependencies": {"@babel/core": "^7.10.2", "coffeescript": "^2.5.1", "less": "^3.11.3 || ^4.0.0", "postcss": "^7 || ^8", "postcss-load-config": ">=3", "pug": "^3.0.0", "sass": "^1.26.8", "stylus": ">=0.55", "sugarss": "^2.0.0 || ^3.0.0 || ^4.0.0", "svelte": "^4.0.0 || ^5.0.0-next.100 || ^5.0.0", "typescript": "^5.0.0"}, "peerDependenciesMeta": {"@babel/core": {"optional": true}, "coffeescript": {"optional": true}, "less": {"optional": true}, "postcss": {"optional": true}, "postcss-load-config": {"optional": true}, "pug": {"optional": true}, "sass": {"optional": true}, "stylus": {"optional": true}, "sugarss": {"optional": true}, "typescript": {"optional": true}}}