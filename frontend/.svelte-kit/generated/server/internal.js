
import root from '../root.js';
import { set_building, set_prerendering } from '__sveltekit/environment';
import { set_assets } from '__sveltekit/paths';
import { set_manifest, set_read_implementation } from '__sveltekit/server';
import { set_private_env, set_public_env, set_safe_public_env } from '../../../node_modules/@sveltejs/kit/src/runtime/shared-server.js';

export const options = {
	app_template_contains_nonce: false,
	csp: {"mode":"auto","directives":{"upgrade-insecure-requests":false,"block-all-mixed-content":false},"reportOnly":{"upgrade-insecure-requests":false,"block-all-mixed-content":false}},
	csrf_check_origin: true,
	embedded: false,
	env_public_prefix: 'PUBLIC_',
	env_private_prefix: '',
	hash_routing: false,
	hooks: null, // added lazily, via `get_hooks`
	preload_strategy: "modulepreload",
	root,
	service_worker: false,
	templates: {
		app: ({ head, body, assets, nonce, env }) => "<!doctype html>\n<html lang=\"en\">\n\t<head>\n\t\t<meta charset=\"utf-8\" />\n\t\t<link rel=\"icon\" href=\"" + assets + "/favicon.png\" />\n\t\t<meta name=\"viewport\" content=\"width=device-width, initial-scale=1\" />\n\t\t<meta name=\"description\" content=\"Your personal workspace for managing tasks, achievements, and future plans\" />\n\t\t<meta name=\"theme-color\" content=\"#4f46e5\" />\n\n\t\t<!-- Preload critical assets -->\n\t\t<link rel=\"preconnect\" href=\"https://fonts.googleapis.com\" />\n\t\t<link rel=\"preconnect\" href=\"https://fonts.gstatic.com\" crossorigin />\n\n\t\t<!-- Prevent flash of unstyled content -->\n\t\t<script>\n\t\t\t// Check for dark mode preference\n\t\t\t(function() {\n\t\t\t\tconst prefersDark = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;\n\t\t\t\tconst storedTheme = localStorage.getItem('app_theme_settings');\n\t\t\t\tlet darkMode = prefersDark;\n\n\t\t\t\t// Check stored theme settings\n\t\t\t\tif (storedTheme) {\n\t\t\t\t\ttry {\n\t\t\t\t\t\tconst settings = JSON.parse(storedTheme);\n\t\t\t\t\t\tdarkMode = settings.darkMode;\n\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\tconsole.error('Failed to parse theme settings');\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// Apply dark mode immediately to prevent flash\n\t\t\t\tif (darkMode) {\n\t\t\t\t\tdocument.documentElement.classList.add('dark');\n\t\t\t\t}\n\n\t\t\t\t// Apply custom background if set\n\t\t\t\tif (storedTheme) {\n\t\t\t\t\ttry {\n\t\t\t\t\t\tconst settings = JSON.parse(storedTheme);\n\t\t\t\t\t\tif (settings.customBackground) {\n\t\t\t\t\t\t\tdocument.documentElement.style.setProperty(\n\t\t\t\t\t\t\t\t'--custom-background',\n\t\t\t\t\t\t\t\t`url(${settings.customBackground})`\n\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\tdocument.documentElement.classList.add('has-custom-bg');\n\t\t\t\t\t\t\t// 确保背景在页面加载时立即可见\n\t\t\t\t\t\t\tdocument.addEventListener('DOMContentLoaded', function() {\n\t\t\t\t\t\t\t\tdocument.body.classList.add('has-background');\n\t\t\t\t\t\t\t\tdocument.body.style.backgroundColor = 'transparent';\n\t\t\t\t\t\t\t\tdocument.documentElement.style.backgroundColor = 'transparent';\n\n\t\t\t\t\t\t\t\t// 如果是暗色模式，添加特殊类\n\t\t\t\t\t\t\t\tif (darkMode) {\n\t\t\t\t\t\t\t\t\tdocument.body.classList.add('dark-with-background');\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tdocument.documentElement.style.setProperty('--custom-background', 'none');\n\t\t\t\t\t\t\tdocument.documentElement.classList.remove('has-custom-bg');\n\t\t\t\t\t\t}\n\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\tconsole.error('Failed to apply custom background');\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t})();\n\t\t</script>\n\n\t\t" + head + "\n\t</head>\n\t<body data-sveltekit-preload-data=\"hover\">\n\t\t<div style=\"display: contents\">" + body + "</div>\n\t\t<script src=\"" + assets + "/js/anchor-button.js\"></script>\n\t</body>\n</html>\n",
		error: ({ status, message }) => "<!doctype html>\n<html lang=\"en\">\n\t<head>\n\t\t<meta charset=\"utf-8\" />\n\t\t<title>" + message + "</title>\n\n\t\t<style>\n\t\t\tbody {\n\t\t\t\t--bg: white;\n\t\t\t\t--fg: #222;\n\t\t\t\t--divider: #ccc;\n\t\t\t\tbackground: var(--bg);\n\t\t\t\tcolor: var(--fg);\n\t\t\t\tfont-family:\n\t\t\t\t\tsystem-ui,\n\t\t\t\t\t-apple-system,\n\t\t\t\t\tBlinkMacSystemFont,\n\t\t\t\t\t'Segoe UI',\n\t\t\t\t\tRoboto,\n\t\t\t\t\tOxygen,\n\t\t\t\t\tUbuntu,\n\t\t\t\t\tCantarell,\n\t\t\t\t\t'Open Sans',\n\t\t\t\t\t'Helvetica Neue',\n\t\t\t\t\tsans-serif;\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tjustify-content: center;\n\t\t\t\theight: 100vh;\n\t\t\t\tmargin: 0;\n\t\t\t}\n\n\t\t\t.error {\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tmax-width: 32rem;\n\t\t\t\tmargin: 0 1rem;\n\t\t\t}\n\n\t\t\t.status {\n\t\t\t\tfont-weight: 200;\n\t\t\t\tfont-size: 3rem;\n\t\t\t\tline-height: 1;\n\t\t\t\tposition: relative;\n\t\t\t\ttop: -0.05rem;\n\t\t\t}\n\n\t\t\t.message {\n\t\t\t\tborder-left: 1px solid var(--divider);\n\t\t\t\tpadding: 0 0 0 1rem;\n\t\t\t\tmargin: 0 0 0 1rem;\n\t\t\t\tmin-height: 2.5rem;\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t}\n\n\t\t\t.message h1 {\n\t\t\t\tfont-weight: 400;\n\t\t\t\tfont-size: 1em;\n\t\t\t\tmargin: 0;\n\t\t\t}\n\n\t\t\t@media (prefers-color-scheme: dark) {\n\t\t\t\tbody {\n\t\t\t\t\t--bg: #222;\n\t\t\t\t\t--fg: #ddd;\n\t\t\t\t\t--divider: #666;\n\t\t\t\t}\n\t\t\t}\n\t\t</style>\n\t</head>\n\t<body>\n\t\t<div class=\"error\">\n\t\t\t<span class=\"status\">" + status + "</span>\n\t\t\t<div class=\"message\">\n\t\t\t\t<h1>" + message + "</h1>\n\t\t\t</div>\n\t\t</div>\n\t</body>\n</html>\n"
	},
	version_hash: "deucjd"
};

export async function get_hooks() {
	let handle;
	let handleFetch;
	let handleError;
	let init;
	

	let reroute;
	let transport;
	

	return {
		handle,
		handleFetch,
		handleError,
		init,
		reroute,
		transport
	};
}

export { set_assets, set_building, set_manifest, set_prerendering, set_private_env, set_public_env, set_read_implementation, set_safe_public_env };
