import{t as u,a as v}from"../chunks/CEORzCZH.js";import{p as ie,x as a,w as g,i as e,c as s,r as l,a as se,s as n,t as R,n as J,P as oe,f as be}from"../chunks/B9CKrN7X.js";import{s as O,e as ae,r as Z,d as xe}from"../chunks/Cc0s-Eqn.js";import{i as I}from"../chunks/C9zVtFY0.js";import{a as le,s as ne}from"../chunks/BIYeehul.js";import{w as Pe,o as de}from"../chunks/D6jnoBFN.js";import{b as ce}from"../chunks/DNwzTOhB.js";import"../chunks/Dj8TIuzp.js";import{r as _e}from"../chunks/DUXSSeDn.js";import{b as N}from"../chunks/Bk_lFxuP.js";import{p as we}from"../chunks/CWmzcjye.js";import{i as Se}from"../chunks/ZU85TcGt.js";async function ke(){try{console.log("AnchorService: Fetching identity profile");const t=await ce.get("/anchor/profile");if(!t)throw console.error("AnchorService: Profile data not found or API returned an unexpected empty response"),new Error("Profile data not found or API returned an unexpected empty response.");return console.log("AnchorService: Successfully fetched identity profile",t),t}catch(t){if(console.error("AnchorService: Failed to fetch identity profile",t),t instanceof Error){console.error("Error details:",{message:t.message,stack:t.stack,name:t.name});const r=new Error(`Failed to fetch identity profile: ${t.message}`);throw r.stack=t.stack,r}throw t}}async function qe(t){try{console.log("AnchorService: Updating identity profile with payload:",t);const r=await ce.put("/anchor/profile",t);if(!r)throw console.error("AnchorService: Updated profile data not found or API returned an unexpected empty response"),new Error("Updated profile data not found or API returned an unexpected empty response.");return console.log("AnchorService: Successfully updated identity profile",r),r}catch(r){if(console.error("AnchorService: Failed to update identity profile",r),r instanceof Error){console.error("Error details:",{message:r.message,stack:r.stack,name:r.name});const d=new Error(`Failed to update identity profile: ${r.message}`);throw d.stack=r.stack,d}throw r}}const fe={getIdentityProfile:ke,updateIdentityProfile:qe},Ae={profile:null,isLoading:!1,error:null},Ee={identityProfile:Ae},{subscribe:Ie,set:Le,update:L}=Pe(Ee);async function $e(){console.log("AnchorStore: Starting loadIdentityProfile"),L(t=>({...t,identityProfile:{...t.identityProfile,isLoading:!0,error:null}}));try{console.log("AnchorStore: Calling anchorService.getIdentityProfile");const t=await fe.getIdentityProfile();console.log("AnchorStore: Successfully received profile data:",t),L(r=>({...r,identityProfile:{profile:t,isLoading:!1,error:null}}))}catch(t){const r=t;throw console.error("AnchorStore: Error fetching identity profile",r),r instanceof Error&&console.error("AnchorStore: Error details:",{message:r.message,stack:r.stack,name:r.name}),L(d=>({...d,identityProfile:{...d.identityProfile,isLoading:!1,error:r.message||"Failed to fetch identity profile."}})),r}}async function De(t){L(r=>({...r,identityProfile:{...r.identityProfile,isLoading:!0,error:null}}));try{const r=await fe.updateIdentityProfile(t);return L(d=>({...d,identityProfile:{profile:r,isLoading:!1,error:null}})),r}catch(r){const d=r;return console.error("AnchorStore: Error saving identity profile",d),L($=>({...$,identityProfile:{...$.identityProfile,isLoading:!1,error:d.message||"Failed to save identity profile."}})),null}}const M={subscribe:Ie,set:Le,update:L,loadIdentityProfile:$e,saveIdentityProfile:De};var Fe=u('<div class="message loading-message svelte-1q4ar04"><p class="svelte-1q4ar04">Loading profile information...</p></div>'),Me=u('<div class="message error-message svelte-1q4ar04"><p class="svelte-1q4ar04"> </p> <button class="retry-button svelte-1q4ar04">Try Again</button></div>'),Te=u('<div class="message error-message svelte-1q4ar04" aria-live="assertive"><p class="svelte-1q4ar04"> </p></div>'),je=u('<div class="message success-message svelte-1q4ar04" aria-live="polite"><p class="svelte-1q4ar04"> </p></div>'),Ue=u("<span>Saving...</span>"),Ye=u("<span>Save Identity Profile</span>"),Be=u('<form class="identity-form svelte-1q4ar04"><div class="form-section svelte-1q4ar04"><h3 class="section-title svelte-1q4ar04">Your Professional Identity</h3> <p class="section-description svelte-1q4ar04">Define your core professional standing. This helps anchor your activities and goals.</p></div> <div class="form-group svelte-1q4ar04"><label for="professional-title" class="svelte-1q4ar04">Professional Title</label> <input type="text" id="professional-title" placeholder="e.g., Senior Software Engineer, Product Manager, Aspiring Data Scientist" maxlength="100" class="svelte-1q4ar04"> <small class="field-hint svelte-1q4ar04">Your current or target professional role.</small></div> <div class="form-group svelte-1q4ar04"><label for="one-liner-bio" class="svelte-1q4ar04">One-Liner Bio / Professional Summary</label> <textarea id="one-liner-bio" placeholder="e.g., Passionate about building scalable web applications and leading dynamic teams." rows="3" maxlength="250" class="svelte-1q4ar04"></textarea> <small class="field-hint svelte-1q4ar04">A concise summary of who you are professionally (max 250 chars).</small></div> <div class="form-group svelte-1q4ar04"><label for="skill" class="svelte-1q4ar04">Skills</label> <textarea id="skill" placeholder="e.g., JavaScript, React, Node.js, Project Management" rows="2" maxlength="250" class="svelte-1q4ar04"></textarea> <small class="field-hint svelte-1q4ar04">Your key skills and competencies (max 250 chars).</small></div> <div class="form-group svelte-1q4ar04"><label for="summary" class="svelte-1q4ar04">Detailed Summary</label> <textarea id="summary" placeholder="e.g., I have 5 years of experience in web development with a focus on frontend technologies..." rows="4" maxlength="500" class="svelte-1q4ar04"></textarea> <small class="field-hint svelte-1q4ar04">A more detailed description of your professional background (max 500 chars).</small></div> <!> <!> <div class="form-actions svelte-1q4ar04"><button type="submit" class="submit-button svelte-1q4ar04"><!></button></div></form>'),Ce=u('<div class="identity-anchor-editor-card svelte-1q4ar04"><!></div>');function Re(t,r){ie(r,!1);const[d,$]=le(),k=()=>ne(M,"$anchorStore",d);let h=g(""),x=g(""),b=g(""),q=g(""),f=g(!1),P=g(""),o=g(""),c=g(null),T=g(!1),D=g(null);const _=M.subscribe(p=>{a(c,p.identityProfile.profile),a(T,p.identityProfile.isLoading),a(D,p.identityProfile.error),e(c)&&!e(f)&&(a(h,e(c).professional_title||""),a(x,e(c).one_liner_bio||""),a(b,e(c).skill||""),a(q,e(c).summary||""))});de(()=>(!e(c)&&!e(T)&&e(D),()=>{_()}));async function w(){if(a(f,!0),a(P,""),a(o,""),!e(h).trim()&&!e(x).trim()&&!e(b).trim()&&!e(q).trim()){a(P,"Please provide at least one field."),a(f,!1);return}const p={professional_title:e(h).trim()||null,one_liner_bio:e(x).trim()||null,skill:e(b).trim()||null,summary:e(q).trim()||null};try{await M.saveIdentityProfile(p)?(a(o,"Profile updated successfully!"),setTimeout(()=>a(o,""),3e3)):a(P,k().identityProfile.error||"Failed to save profile. Please try again.")}catch(A){const U=A;a(P,(U==null?void 0:U.message)||"An unexpected error occurred while saving."),console.error("IdentityAnchorEditor handleSubmit error:",A)}finally{a(f,!1)}}Se();var y=Ce(),j=s(y);{var z=p=>{var A=Fe();v(p,A)},pe=(p,A)=>{{var U=F=>{var S=Me(),E=s(S),Y=s(E);l(E);var B=n(E,2);l(S),R(()=>O(Y,`Error loading profile: ${e(D)??""}`)),ae("click",B,()=>M.loadIdentityProfile()),v(F,S)},ve=F=>{var S=Be(),E=n(s(S),2),Y=n(s(E),2);_e(Y),J(2),l(E);var B=n(E,2),G=n(s(B),2);Z(G),J(2),l(B);var H=n(B,2),K=n(s(H),2);Z(K),J(2),l(H);var Q=n(H,2),V=n(s(Q),2);Z(V),J(2),l(Q);var ee=n(Q,2);{var ue=i=>{var m=Te(),C=s(m),X=s(C,!0);l(C),l(m),R(()=>O(X,e(P))),v(i,m)};I(ee,i=>{e(P)&&i(ue)})}var re=n(ee,2);{var me=i=>{var m=je(),C=s(m),X=s(C,!0);l(C),l(m),R(()=>O(X,e(o))),v(i,m)};I(re,i=>{e(o)&&i(me)})}var te=n(re,2),W=s(te),ye=s(W);{var ge=i=>{var m=Ue();v(i,m)},he=i=>{var m=Ye();v(i,m)};I(ye,i=>{e(f)?i(ge):i(he,!1)})}l(W),l(te),l(S),R(()=>{Y.disabled=e(f),G.disabled=e(f),K.disabled=e(f),V.disabled=e(f),W.disabled=e(f)}),N(Y,()=>e(h),i=>a(h,i)),N(G,()=>e(x),i=>a(x,i)),N(K,()=>e(b),i=>a(b,i)),N(V,()=>e(q),i=>a(q,i)),ae("submit",S,we(w)),v(F,S)};I(p,F=>{e(D)&&!e(c)?F(U):F(ve,!1)},A)}};I(j,p=>{e(T)&&!e(c)?p(z):p(pe,!1)})}l(y),v(t,y),se(),$()}var Je=u('<div class="text-center py-12"><div class="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-500 mb-4"></div> <p class="text-gray-600 dark:text-gray-400">Loading your identity anchor...</p></div>'),Ne=()=>window.location.reload(),Oe=u('<div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6 text-center"><h2 class="text-xl font-semibold text-red-700 dark:text-red-400 mb-2">Error Loading Page</h2> <p class="text-red-600 dark:text-red-300 mb-4"> </p> <button class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md shadow-sm">Reload Page</button></div>'),ze=u(`<header class="text-center mb-10 pb-6 border-b border-gray-200 dark:border-gray-700"><span class="text-5xl block mb-2 text-primary-500" aria-hidden="true">⚓️</span> <h1 class="text-4xl font-bold text-gray-900 dark:text-gray-100 mb-3">My Identity Anchor</h1> <p class="text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto leading-relaxed">This is your foundational space. Define "who you are" professionally to guide your actions,
          reflect on your journey, and plan your future with clarity.</p></header> <section><!></section>`,1),Ge=u('<div class="max-w-4xl mx-auto px-6 py-8"><!></div>');function ir(t,r){ie(r,!0);const[d,$]=le(),k=()=>ne(M,"$anchorStore",d);let h=oe(null),x=oe(!0);de(async()=>{try{if(console.log("Anchor page mounted, checking profile state"),!k().identityProfile.profile&&!k().identityProfile.isLoading){console.log("Loading identity profile from anchor page");try{await M.loadIdentityProfile(),console.log("Identity profile loaded successfully")}catch(o){throw console.error("Error in loadIdentityProfile call:",o),o instanceof Error&&console.error("Error details:",{message:o.message,stack:o.stack,name:o.name}),o}}console.log("Identity profile state after load:",{profile:k().identityProfile.profile?"exists":"null",isLoading:k().identityProfile.isLoading,error:k().identityProfile.error})}catch(o){console.error("Error loading identity profile from anchor page:",o),a(h,o instanceof Error?o.message:"An unexpected error occurred loading the profile",!0)}finally{a(x,!1)}});var b=Ge(),q=s(b);{var f=o=>{var c=Je();v(o,c)},P=(o,c)=>{{var T=_=>{var w=Oe(),y=n(s(w),2),j=s(y,!0);l(y);var z=n(y,2);z.__click=[Ne],l(w),R(()=>O(j,e(h))),v(_,w)},D=_=>{var w=ze(),y=n(be(w),2),j=s(y);Re(j,{}),l(y),v(_,w)};I(o,_=>{e(h)?_(T):_(D,!1)},c)}};I(q,o=>{e(x)?o(f):o(P,!1)})}l(b),v(t,b),se(),$()}xe(["click"]);export{ir as component};
