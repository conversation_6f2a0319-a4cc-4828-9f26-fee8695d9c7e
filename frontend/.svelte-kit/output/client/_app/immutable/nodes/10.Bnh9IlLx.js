import{t as g,a as t,c as We,d as c}from"../chunks/CEORzCZH.js";import"../chunks/Dj8TIuzp.js";import{t as k,c as d,r as o,i as l,Q as z,s,p as Be,a as De,f as we,n as u,x as pe,w as $e}from"../chunks/B9CKrN7X.js";import{s as B,e as la}from"../chunks/Cc0s-Eqn.js";import{i as H}from"../chunks/C9zVtFY0.js";import{p as oa}from"../chunks/CWmzcjye.js";import{A as be}from"../chunks/hBTlxZOa.js";import{a as fe,r as Je,s as qe,b as _e}from"../chunks/DUXSSeDn.js";import{p as i,r as ke}from"../chunks/B16-Q6Ob.js";import{s as da,e as ze,i as Me}from"../chunks/D8moldTO.js";import{s as na,c as ia}from"../chunks/BIuBsydj.js";import{b as Re}from"../chunks/Bk_lFxuP.js";import{b as Xe}from"../chunks/Cj49lUop.js";import"../chunks/BCVqPHae.js";var va=g("<span> </span>");function te(ae,r){let M=i(r,"variant",3,"default"),_=i(r,"size",3,"md"),p=i(r,"rounded",3,"full"),D=i(r,"class",3,""),G=ke(r,["$$slots","$$events","$$legacy","variant","size","rounded","class","children"]);const O={default:"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300",primary:"bg-primary-100 text-primary-800 dark:bg-primary-900/30 dark:text-primary-300",secondary:"bg-secondary-100 text-secondary-800 dark:bg-secondary-900/30 dark:text-secondary-300",danger:"bg-danger-100 text-danger-800 dark:bg-danger-900/30 dark:text-danger-300",success:"bg-success-100 text-success-800 dark:bg-success-900/30 dark:text-success-300",warning:"bg-warning-100 text-warning-800 dark:bg-warning-900/30 dark:text-warning-300",info:"bg-info-100 text-info-800 dark:bg-info-900/30 dark:text-info-300",outline:"bg-transparent border border-gray-300 text-gray-700 dark:border-gray-600 dark:text-gray-300"},L={sm:"px-2 py-0.5 text-xs",md:"px-2.5 py-0.5 text-xs",lg:"px-3 py-1 text-sm"},N={none:"rounded-none",sm:"rounded-sm",md:"rounded-md",lg:"rounded-lg",xl:"rounded-xl",full:"rounded-full"},E=z(()=>O[M()]),I=z(()=>L[_()]),j=z(()=>N[p()]),J=z(()=>`
    inline-flex items-center font-medium
    ${l(E)}
    ${l(I)}
    ${l(j)}
    ${D()}
  `);var F=va();let V;var P=d(F,!0);o(F),k(()=>{V=fe(F,V,{class:l(J),...G}),B(P,r.children)}),t(ae,F)}var ua=g('<span class="mr-2 inline-block animate-spin"><svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 12a9 9 0 1 1-6.219-8.56"></path></svg></span>'),ca=g("<button><!> <!></button>");function q(ae,r){let M=i(r,"variant",3,"default"),_=i(r,"size",3,"md"),p=i(r,"type",3,"button"),D=i(r,"disabled",3,!1),G=i(r,"loading",3,!1),O=i(r,"fullWidth",3,!1),L=i(r,"class",3,""),N=ke(r,["$$slots","$$events","$$legacy","variant","size","type","disabled","loading","fullWidth","class","onclick","onfocus","onblur","onmouseenter","onmouseleave"]);const E={default:"bg-gray-100 text-gray-900 hover:bg-gray-200 focus:ring-gray-400 dark:bg-gray-700 dark:text-gray-100 dark:hover:bg-gray-600",primary:"bg-primary-500 text-white hover:bg-primary-600 focus:ring-primary-400 dark:bg-primary-600 dark:hover:bg-primary-700",secondary:"bg-secondary-500 text-white hover:bg-secondary-600 focus:ring-secondary-400 dark:bg-secondary-600 dark:hover:bg-secondary-700",danger:"bg-danger-500 text-white hover:bg-danger-600 focus:ring-danger-400 dark:bg-danger-600 dark:hover:bg-danger-700",success:"bg-success-500 text-white hover:bg-success-600 focus:ring-success-400 dark:bg-success-600 dark:hover:bg-success-700",warning:"bg-warning-500 text-white hover:bg-warning-600 focus:ring-warning-400 dark:bg-warning-600 dark:hover:bg-warning-700",info:"bg-info-500 text-white hover:bg-info-600 focus:ring-info-400 dark:bg-info-600 dark:hover:bg-info-700",outline:"border border-gray-300 bg-transparent text-gray-700 hover:bg-gray-50 focus:ring-gray-400 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-800",ghost:"bg-transparent text-gray-700 hover:bg-gray-100 focus:ring-gray-400 dark:text-gray-300 dark:hover:bg-gray-800"},I={sm:"px-3 py-1.5 text-xs",md:"px-4 py-2 text-sm",lg:"px-6 py-3 text-base"},j=z(()=>E[M()]),J=z(()=>I[_()]),F=z(()=>O()?"w-full":""),V=z(()=>`
    inline-flex items-center justify-center
    font-medium rounded-md
    transition-colors duration-200
    focus:outline-none focus:ring-2 focus:ring-offset-2
    disabled:opacity-50 disabled:cursor-not-allowed
    ${l(j)}
    ${l(J)}
    ${l(F)}
    ${L()}
  `);var P=ca();let K;var A=d(P);{var R=Q=>{var X=ua();t(Q,X)};H(A,Q=>{G()&&Q(R)})}var ee=s(A,2);da(ee,r,"default",{},null),o(P),k(()=>K=fe(P,K,{type:p(),class:l(V),disabled:D()||G(),onclick:r.onclick,onfocus:r.onfocus,onblur:r.onblur,onmouseenter:r.onmouseenter,onmouseleave:r.onmouseleave,...N})),t(ae,P)}var ga=g('<div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700"> </div>'),ba=g('<div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/80"> </div>'),fa=g("<div><!> <div> </div> <!></div>");function xe(ae,r){Be(r,!0);let M=i(r,"variant",3,"default"),_=i(r,"padding",3,!0),p=i(r,"shadow",3,"md"),D=i(r,"rounded",3,"lg"),G=i(r,"border",3,!0),O=i(r,"class",3,""),L=ke(r,["$$slots","$$events","$$legacy","variant","padding","shadow","rounded","border","class","children"]);const N={default:"bg-white dark:bg-gray-800",primary:"bg-primary-50 dark:bg-primary-900/20",secondary:"bg-secondary-50 dark:bg-secondary-900/20",danger:"bg-danger-50 dark:bg-danger-900/20",success:"bg-success-50 dark:bg-success-900/20",warning:"bg-warning-50 dark:bg-warning-900/20",info:"bg-info-50 dark:bg-info-900/20"},E={none:"",sm:"shadow-sm",md:"shadow-md",lg:"shadow-lg",xl:"shadow-xl"},I={none:"rounded-none",sm:"rounded-sm",md:"rounded-md",lg:"rounded-lg",xl:"rounded-xl","2xl":"rounded-2xl","3xl":"rounded-3xl",full:"rounded-full"},j=z(()=>N[M()]),J=z(()=>E[p()]),F=z(()=>I[D()]),V=z(()=>G()?M()==="default"?"border border-gray-200 dark:border-gray-700":`border border-${M()}-200 dark:border-${M()}-800`:""),P=z(()=>`
    overflow-hidden
    ${l(j)}
    ${l(J)}
    ${l(F)}
    ${l(V)}
    ${O()}
  `),K=z(()=>_()?"p-6":"");var A=fa();let R;var ee=d(A);{var Q=v=>{var b=ga(),C=d(b,!0);o(b),k(()=>B(C,r.children.header)),t(v,b)};H(ee,v=>{var b;(b=r.children)!=null&&b.header&&v(Q)})}var X=s(ee,2),ie=d(X,!0);o(X);var y=s(X,2);{var m=v=>{var b=ba(),C=d(b,!0);o(b),k(()=>B(C,r.children.footer)),t(v,b)};H(y,v=>{var b;(b=r.children)!=null&&b.footer&&v(m)})}o(A),k(()=>{var v;R=fe(A,R,{class:l(P),...L}),na(X,1,ia(l(K))),B(ie,(v=r.children)==null?void 0:v.default)}),t(ae,A),De()}var ma=g('<span class="text-danger-500 ml-1">*</span>'),ha=g('<p class="mt-1 text-sm text-danger-500"> </p>'),_a=g('<p class="mt-1 text-sm text-gray-500 dark:text-gray-400"> </p>'),xa=g('<div class="mb-4"><label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"> <!></label> <input> <!></div>'),ya=g("<input>");function Ae(ae,r){Be(r,!0);const M=(m,v)=>{const b=new CustomEvent(m,{detail:v});document.dispatchEvent(b)};let _=i(r,"type",3,"text"),p=i(r,"value",7,""),D=i(r,"placeholder",3,""),G=i(r,"label",3,""),O=i(r,"id",3,""),L=i(r,"name",3,""),N=i(r,"disabled",3,!1),E=i(r,"readonly",3,!1),I=i(r,"required",3,!1),j=i(r,"error",3,""),J=i(r,"hint",3,""),F=i(r,"fullWidth",3,!0),V=i(r,"class",3,""),P=ke(r,["$$slots","$$events","$$legacy","type","value","placeholder","label","id","name","disabled","readonly","required","error","hint","fullWidth","class","onfocus","onblur"]);const K=z(()=>O()||`input-${Math.random().toString(36).substring(2,9)}`),A=z(()=>`
    block px-3 py-2 bg-white dark:bg-gray-800
    border rounded-md shadow-sm
    focus:outline-none focus:ring-2 focus:ring-offset-0
    disabled:opacity-50 disabled:cursor-not-allowed
    ${j()?"border-danger-500 focus:border-danger-500 focus:ring-danger-400":"border-gray-300 dark:border-gray-600 focus:border-primary-500 focus:ring-primary-400"}
    ${F()?"w-full":""}
    ${V()}
  `);function R(m){const v=m.target;p(v.value),M("input",{value:p()})}function ee(m){const v=m.target;p(v.value),M("change",{value:p()})}var Q=We(),X=we(Q);{var ie=m=>{var v=xa(),b=d(v),C=d(b),ve=s(C);{var de=W=>{var w=ma();t(W,w)};H(ve,W=>{I()&&W(de)})}o(b);var T=s(b,2);Je(T);let $;var Y=s(T,2);{var ue=W=>{var w=ha(),x=d(w,!0);o(w),k(()=>B(x,j())),t(W,w)},ce=(W,w)=>{{var x=h=>{var f=_a(),S=d(f,!0);o(f),k(()=>B(S,J())),t(h,f)};H(W,h=>{J()&&h(x)},w)}};H(Y,W=>{j()?W(ue):W(ce,!1)})}o(v),k(()=>{qe(b,"for",l(K)),B(C,`${G()??""} `),$=fe(T,$,{type:_(),id:l(K),name:L(),placeholder:D(),disabled:N(),readonly:E(),required:I(),class:l(A),oninput:R,onchange:ee,onfocus:r.onfocus,onblur:r.onblur,...P})}),Re(T,p),t(m,v)},y=m=>{var v=ya();Je(v);let b;k(()=>b=fe(v,b,{type:_(),id:l(K),name:L(),placeholder:D(),disabled:N(),readonly:E(),required:I(),class:l(A),oninput:R,onchange:ee,onfocus:r.onfocus,onblur:r.onblur,...P})),Re(v,p),t(m,v)};H(X,m=>{G()?m(ie):m(y,!1)})}t(ae,Q),De()}var pa=g('<span class="text-danger-500 ml-1">*</span>'),$a=g("<option> </option>"),wa=g("<optgroup></optgroup>"),ka=g("<option> </option>"),Pa=g('<p class="mt-1 text-sm text-danger-500"> </p>'),Ca=g('<p class="mt-1 text-sm text-gray-500 dark:text-gray-400"> </p>'),Sa=g('<div class="mb-4"><label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"> <!></label> <div class="relative"><select><option disabled> </option><!></select> <div class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none"><svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"><path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg></div></div> <!></div>'),za=g("<option> </option>"),Ma=g("<optgroup></optgroup>"),Wa=g("<option> </option>"),qa=g('<div class="relative"><select><option disabled> </option><!></select> <div class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none"><svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"><path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg></div></div>');function Ea(ae,r){Be(r,!0);const M=(y,m)=>{const v=new CustomEvent(y,{detail:m});document.dispatchEvent(v)};let _=i(r,"value",7,""),p=i(r,"options",19,()=>[]),D=i(r,"label",3,""),G=i(r,"id",3,""),O=i(r,"name",3,""),L=i(r,"placeholder",3,"Select an option"),N=i(r,"disabled",3,!1),E=i(r,"required",3,!1),I=i(r,"error",3,""),j=i(r,"hint",3,""),J=i(r,"fullWidth",3,!0),F=i(r,"class",3,""),V=ke(r,["$$slots","$$events","$$legacy","value","options","label","id","name","placeholder","disabled","required","error","hint","fullWidth","class","onfocus","onblur"]);const P=z(()=>G()||`select-${Math.random().toString(36).substring(2,9)}`),K=z(()=>`
    block px-3 py-2 bg-white dark:bg-gray-800
    border rounded-md shadow-sm
    focus:outline-none focus:ring-2 focus:ring-offset-0
    disabled:opacity-50 disabled:cursor-not-allowed
    ${I()?"border-danger-500 focus:border-danger-500 focus:ring-danger-400":"border-gray-300 dark:border-gray-600 focus:border-primary-500 focus:ring-primary-400"}
    ${J()?"w-full":""}
    ${F()}
  `);function A(y){return"options"in y&&Array.isArray(y.options)}function R(y){const m=y.target;_(m.value),M("change",{value:_()})}var ee=We(),Q=we(ee);{var X=y=>{var m=Sa(),v=d(m),b=d(v),C=s(b);{var ve=h=>{var f=pa();t(h,f)};H(C,h=>{E()&&h(ve)})}o(v);var de=s(v,2),T=d(de);let $;var Y=d(T);Y.value=Y.__value="";var ue=d(Y,!0);o(Y);var ce=s(Y);ze(ce,17,p,Me,(h,f)=>{var S=We(),re=we(S);{var se=ne=>{var U=wa();ze(U,21,()=>l(f).options,Me,(ge,le)=>{var oe=$a(),ye={},Pe=d(oe,!0);o(oe),k(()=>{ye!==(ye=l(le).value)&&(oe.value=(oe.__value=l(le).value)??""),oe.disabled=l(le).disabled,_e(oe,_()===l(le).value),B(Pe,l(le).label)}),t(ge,oe)}),o(U),k(()=>{qe(U,"label",l(f).label),U.disabled=l(f).disabled}),t(ne,U)},me=ne=>{var U=ka(),ge={},le=d(U,!0);o(U),k(()=>{ge!==(ge=l(f).value)&&(U.value=(U.__value=l(f).value)??""),U.disabled=l(f).disabled,_e(U,_()===l(f).value),B(le,l(f).label)}),t(ne,U)};H(re,ne=>{A(l(f))?ne(se):ne(me,!1)})}t(h,S)}),o(T),u(2),o(de);var W=s(de,2);{var w=h=>{var f=Pa(),S=d(f,!0);o(f),k(()=>B(S,I())),t(h,f)},x=(h,f)=>{{var S=re=>{var se=Ca(),me=d(se,!0);o(se),k(()=>B(me,j())),t(re,se)};H(h,re=>{j()&&re(S)},f)}};H(W,h=>{I()?h(w):h(x,!1)})}o(m),k(()=>{qe(v,"for",l(P)),B(b,`${D()??""} `),$=fe(T,$,{id:l(P),name:O(),disabled:N(),required:E(),class:l(K),onchange:R,onfocus:r.onfocus,onblur:r.onblur,...V}),_e(Y,!_()),B(ue,L())}),Xe(T,_),t(y,m)},ie=y=>{var m=qa(),v=d(m);let b;var C=d(v);C.value=C.__value="";var ve=d(C,!0);o(C);var de=s(C);ze(de,17,p,Me,(T,$)=>{var Y=We(),ue=we(Y);{var ce=w=>{var x=Ma();ze(x,21,()=>l($).options,Me,(h,f)=>{var S=za(),re={},se=d(S,!0);o(S),k(()=>{re!==(re=l(f).value)&&(S.value=(S.__value=l(f).value)??""),S.disabled=l(f).disabled,_e(S,_()===l(f).value),B(se,l(f).label)}),t(h,S)}),o(x),k(()=>{qe(x,"label",l($).label),x.disabled=l($).disabled}),t(w,x)},W=w=>{var x=Wa(),h={},f=d(x,!0);o(x),k(()=>{h!==(h=l($).value)&&(x.value=(x.__value=l($).value)??""),x.disabled=l($).disabled,_e(x,_()===l($).value),B(f,l($).label)}),t(w,x)};H(ue,w=>{A(l($))?w(ce):w(W,!1)})}t(T,Y)}),o(v),u(2),o(m),k(()=>{b=fe(v,b,{id:l(P),name:O(),disabled:N(),required:E(),class:l(K),onchange:R,onfocus:r.onfocus,onblur:r.onblur,...V}),_e(C,!_()),B(ve,L())}),Xe(v,_),t(y,m)};H(Q,y=>{D()?y(X):y(ie,!1)})}t(ae,ee),De()}var Ia=g("<p>Simple card with just body content</p>"),Aa=g("<p>Card with header and body content</p>"),Ba=g('<h3 class="text-lg font-semibold">Card Title</h3>'),Da=g('<p class="mb-4">Card with header, body, and footer</p>'),La=g('<h3 class="text-lg font-semibold">Complete Card</h3>'),Na=g('<div class="flex justify-end"><!></div>'),ja=g("<p>Card with primary variant</p>"),Fa=g('<h3 class="text-lg font-semibold">Primary Card</h3>'),Ta=g("<p>Card with success variant and large shadow</p>"),Ua=g('<h3 class="text-lg font-semibold">Success Card</h3>'),Ga=g('<form class="space-y-4"><!> <!> <!> <!> <div class="flex justify-end"><!></div></form> <!>',1),Va=g('<h3 class="text-lg font-semibold">Contact Form Example</h3>'),Ka=g('<div class="max-w-7xl mx-auto px-4 py-12"><header class="mb-12 text-center"><h1 class="text-4xl font-bold mb-4">UI Component Library</h1> <p class="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">A collection of reusable Tailwind CSS components for Svelte applications</p></header> <section class="mb-16"><h2 class="text-2xl font-bold mb-6 pb-2 border-b border-gray-200 dark:border-gray-700">Buttons</h2> <div class="mb-8"><h3 class="text-xl font-semibold mb-4">Variants</h3> <div class="flex flex-wrap gap-4"><!> <!> <!> <!> <!> <!> <!> <!> <!></div></div> <div class="mb-8"><h3 class="text-xl font-semibold mb-4">Sizes</h3> <div class="flex flex-wrap items-center gap-4"><!> <!> <!></div></div> <div class="mb-8"><h3 class="text-xl font-semibold mb-4">States</h3> <div class="flex flex-wrap gap-4"><!> <!> <!></div></div></section> <section class="mb-16"><h2 class="text-2xl font-bold mb-6 pb-2 border-b border-gray-200 dark:border-gray-700">Badges</h2> <div class="mb-8"><h3 class="text-xl font-semibold mb-4">Variants</h3> <div class="flex flex-wrap gap-4"><!> <!> <!> <!> <!> <!> <!> <!></div></div> <div class="mb-8"><h3 class="text-xl font-semibold mb-4">Sizes</h3> <div class="flex flex-wrap items-center gap-4"><!> <!> <!></div></div></section> <section class="mb-16"><h2 class="text-2xl font-bold mb-6 pb-2 border-b border-gray-200 dark:border-gray-700">Cards</h2> <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"><!> <!> <!> <!> <!></div></section> <section class="mb-16"><h2 class="text-2xl font-bold mb-6 pb-2 border-b border-gray-200 dark:border-gray-700">Alerts</h2> <div class="space-y-4"><!> <!> <!> <!> <!> <!></div></section> <section class="mb-16"><h2 class="text-2xl font-bold mb-6 pb-2 border-b border-gray-200 dark:border-gray-700">Form Elements</h2> <!></section></div>');function or(ae){const r=[{value:"us",label:"United States"},{value:"ca",label:"Canada"},{value:"mx",label:"Mexico"},{value:"uk",label:"United Kingdom"},{value:"fr",label:"France"}];let M=$e(""),_=$e(""),p=$e(""),D=$e(""),G=$e(!1);function O(){pe(G,!0),console.log({name:l(M),email:l(_),country:l(p),message:l(D)})}var L=Ka(),N=s(d(L),2),E=s(d(N),2),I=s(d(E),2),j=d(I);q(j,{children:(a,n)=>{u();var e=c("Default");t(a,e)},$$slots:{default:!0}});var J=s(j,2);q(J,{variant:"primary",children:(a,n)=>{u();var e=c("Primary");t(a,e)},$$slots:{default:!0}});var F=s(J,2);q(F,{variant:"secondary",children:(a,n)=>{u();var e=c("Secondary");t(a,e)},$$slots:{default:!0}});var V=s(F,2);q(V,{variant:"success",children:(a,n)=>{u();var e=c("Success");t(a,e)},$$slots:{default:!0}});var P=s(V,2);q(P,{variant:"danger",children:(a,n)=>{u();var e=c("Danger");t(a,e)},$$slots:{default:!0}});var K=s(P,2);q(K,{variant:"warning",children:(a,n)=>{u();var e=c("Warning");t(a,e)},$$slots:{default:!0}});var A=s(K,2);q(A,{variant:"info",children:(a,n)=>{u();var e=c("Info");t(a,e)},$$slots:{default:!0}});var R=s(A,2);q(R,{variant:"outline",children:(a,n)=>{u();var e=c("Outline");t(a,e)},$$slots:{default:!0}});var ee=s(R,2);q(ee,{variant:"ghost",children:(a,n)=>{u();var e=c("Ghost");t(a,e)},$$slots:{default:!0}}),o(I),o(E);var Q=s(E,2),X=s(d(Q),2),ie=d(X);q(ie,{variant:"primary",size:"sm",children:(a,n)=>{u();var e=c("Small");t(a,e)},$$slots:{default:!0}});var y=s(ie,2);q(y,{variant:"primary",size:"md",children:(a,n)=>{u();var e=c("Medium");t(a,e)},$$slots:{default:!0}});var m=s(y,2);q(m,{variant:"primary",size:"lg",children:(a,n)=>{u();var e=c("Large");t(a,e)},$$slots:{default:!0}}),o(X),o(Q);var v=s(Q,2),b=s(d(v),2),C=d(b);q(C,{variant:"primary",disabled:!0,children:(a,n)=>{u();var e=c("Disabled");t(a,e)},$$slots:{default:!0}});var ve=s(C,2);q(ve,{variant:"primary",loading:!0,children:(a,n)=>{u();var e=c("Loading");t(a,e)},$$slots:{default:!0}});var de=s(ve,2);q(de,{variant:"primary",fullWidth:!0,children:(a,n)=>{u();var e=c("Full Width");t(a,e)},$$slots:{default:!0}}),o(b),o(v),o(N);var T=s(N,2),$=s(d(T),2),Y=s(d($),2),ue=d(Y);te(ue,{children:(a,n)=>{u();var e=c("Default");t(a,e)},$$slots:{default:!0}});var ce=s(ue,2);te(ce,{variant:"primary",children:(a,n)=>{u();var e=c("Primary");t(a,e)},$$slots:{default:!0}});var W=s(ce,2);te(W,{variant:"secondary",children:(a,n)=>{u();var e=c("Secondary");t(a,e)},$$slots:{default:!0}});var w=s(W,2);te(w,{variant:"success",children:(a,n)=>{u();var e=c("Success");t(a,e)},$$slots:{default:!0}});var x=s(w,2);te(x,{variant:"danger",children:(a,n)=>{u();var e=c("Danger");t(a,e)},$$slots:{default:!0}});var h=s(x,2);te(h,{variant:"warning",children:(a,n)=>{u();var e=c("Warning");t(a,e)},$$slots:{default:!0}});var f=s(h,2);te(f,{variant:"info",children:(a,n)=>{u();var e=c("Info");t(a,e)},$$slots:{default:!0}});var S=s(f,2);te(S,{variant:"outline",children:(a,n)=>{u();var e=c("Outline");t(a,e)},$$slots:{default:!0}}),o(Y),o($);var re=s($,2),se=s(d(re),2),me=d(se);te(me,{variant:"primary",size:"sm",children:(a,n)=>{u();var e=c("Small");t(a,e)},$$slots:{default:!0}});var ne=s(me,2);te(ne,{variant:"primary",size:"md",children:(a,n)=>{u();var e=c("Medium");t(a,e)},$$slots:{default:!0}});var U=s(ne,2);te(U,{variant:"primary",size:"lg",children:(a,n)=>{u();var e=c("Large");t(a,e)},$$slots:{default:!0}}),o(se),o(re),o(T);var ge=s(T,2),le=s(d(ge),2),oe=d(le);xe(oe,{children:(a,n)=>{var e=Ia();t(a,e)},$$slots:{default:!0}});var ye=s(oe,2);xe(ye,{children:(a,n)=>{var e=Aa();t(a,e)},$$slots:{default:!0,header:(a,n)=>{var e=Ba();t(a,e)}}});var Pe=s(ye,2);xe(Pe,{children:(a,n)=>{var e=Da();t(a,e)},$$slots:{default:!0,header:(a,n)=>{var e=La();t(a,e)},footer:(a,n)=>{var e=Na(),he=d(e);q(he,{variant:"primary",children:(Ce,Ie)=>{u();var Se=c("Action");t(Ce,Se)},$$slots:{default:!0}}),o(e),t(a,e)}}});var Le=s(Pe,2);xe(Le,{variant:"primary",children:(a,n)=>{var e=ja();t(a,e)},$$slots:{default:!0,header:(a,n)=>{var e=Fa();t(a,e)}}});var Ze=s(Le,2);xe(Ze,{variant:"success",shadow:"lg",children:(a,n)=>{var e=Ta();t(a,e)},$$slots:{default:!0,header:(a,n)=>{var e=Ua();t(a,e)}}}),o(le),o(ge);var Ee=s(ge,2),Ne=s(d(Ee),2),je=d(Ne);be(je,{children:(a,n)=>{u();var e=c("Default alert message");t(a,e)},$$slots:{default:!0}});var Fe=s(je,2);be(Fe,{variant:"primary",children:(a,n)=>{u();var e=c("Primary alert message");t(a,e)},$$slots:{default:!0}});var Te=s(Fe,2);be(Te,{variant:"success",title:"Success",children:(a,n)=>{u();var e=c("Operation completed successfully");t(a,e)},$$slots:{default:!0}});var Ue=s(Te,2);be(Ue,{variant:"danger",title:"Error",dismissible:!0,children:(a,n)=>{u();var e=c("Something went wrong. Please try again.");t(a,e)},$$slots:{default:!0}});var Ge=s(Ue,2);be(Ge,{variant:"warning",title:"Warning",dismissible:!0,children:(a,n)=>{u();var e=c("Your account is about to expire");t(a,e)},$$slots:{default:!0}});var Oe=s(Ge,2);be(Oe,{variant:"info",title:"Information",children:(a,n)=>{u();var e=c("This is an informational message");t(a,e)},$$slots:{default:!0}}),o(Ne),o(Ee);var Ve=s(Ee,2),ea=s(d(Ve),2);xe(ea,{children:(a,n)=>{var e=Ga(),he=we(e),Ce=d(he);Ae(Ce,{label:"Name",type:"text",placeholder:"Enter your name",required:!0,get value(){return l(M)},set value(Z){pe(M,Z)},$$legacy:!0});var Ie=s(Ce,2);Ae(Ie,{label:"Email",type:"email",placeholder:"Enter your email",required:!0,get value(){return l(_)},set value(Z){pe(_,Z)},$$legacy:!0});var Se=s(Ie,2);Ea(Se,{label:"Country",options:r,placeholder:"Select your country",get value(){return l(p)},set value(Z){pe(p,Z)},$$legacy:!0});var Ke=s(Se,2);Ae(Ke,{label:"Message",type:"textarea",placeholder:"Enter your message",required:!0,get value(){return l(D)},set value(Z){pe(D,Z)},$$legacy:!0});var Qe=s(Ke,2),aa=d(Qe);q(aa,{variant:"primary",type:"submit",children:(Z,Ye)=>{u();var He=c("Submit");t(Z,He)},$$slots:{default:!0}}),o(Qe),o(he);var ra=s(he,2);{var ta=Z=>{be(Z,{variant:"success",class:"mt-4",children:(Ye,He)=>{u();var sa=c("Form submitted successfully!");t(Ye,sa)},$$slots:{default:!0}})};H(ra,Z=>{l(G)&&Z(ta)})}la("submit",he,oa(O)),t(a,e)},$$slots:{default:!0,header:(a,n)=>{var e=Va();t(a,e)}}}),o(Ve),o(L),t(ae,L)}export{or as component};
