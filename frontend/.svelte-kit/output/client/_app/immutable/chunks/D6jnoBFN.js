import{v as l,g as y,aa as x,e as k,d as z,b as p,a2 as A}from"./B9CKrN7X.js";function h(e){throw new Error("https://svelte.dev/e/lifecycle_outside_component")}function w(e,n,s){if(e==null)return n(void 0),s&&s(void 0),l;const r=y(()=>e.subscribe(n,s));return r.unsubscribe?()=>r.unsubscribe():r}const f=[];function E(e,n){return{subscribe:B(e,n).subscribe}}function B(e,n=l){let s=null;const r=new Set;function c(u){if(x(e,u)&&(e=u,s)){const o=!f.length;for(const t of r)t[1](),f.push(t,e);if(o){for(let t=0;t<f.length;t+=2)f[t][0](f[t+1]);f.length=0}}}function a(u){c(u(e))}function b(u,o=l){const t=[u,o];return r.add(t),r.size===1&&(s=n(c,a)||l),u(e),()=>{r.delete(t),r.size===0&&s&&(s(),s=null)}}return{set:c,update:a,subscribe:b}}function j(e,n,s){const r=!Array.isArray(e),c=r?[e]:e;if(!c.every(Boolean))throw new Error("derived() expects stores as input, got a falsy value");const a=n.length<2;return E(s,(b,u)=>{let o=!1;const t=[];let d=0,_=l;const m=()=>{if(d)return;_();const i=n(r?t[0]:t,b,u);a?b(i):_=typeof i=="function"?i:l},q=c.map((i,g)=>w(i,v=>{t[g]=v,d&=~(1<<g),o&&m()},()=>{d|=1<<g}));return o=!0,m(),function(){k(q),_(),o=!1}})}function C(e){let n;return w(e,s=>n=s)(),n}function D(e){p===null&&h(),A&&p.l!==null?M(p).m.push(e):z(()=>{const n=y(e);if(typeof n=="function")return n})}function F(e){p===null&&h(),D(()=>()=>y(e))}function M(e){var n=e.l;return n.u??(n.u={a:[],b:[],m:[]})}export{F as a,j as d,C as g,D as o,w as s,B as w};
