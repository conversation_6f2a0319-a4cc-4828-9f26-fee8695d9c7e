import{w as u}from"./D6jnoBFN.js";const l={notifications:[]},a=u(l);function d(){return`notification-${Date.now()}-${Math.random().toString(36).substr(2,9)}`}function t(i,o,n={}){if(!o||typeof o!="string")return console.warn("NotificationStore: 无效的消息内容",o),"";["success","error","warning","info"].includes(i)||(console.warn("NotificationStore: 无效的通知类型",i),i="info");const s=d(),r={id:s,type:i,message:o.trim(),duration:n.duration??(i==="success"?3e3:i==="error"?5e3:4e3),dismissible:n.dismissible??!0};try{a.update(c=>{let e=[...c.notifications,r];return e.length>5&&(e=e.slice(-5)),{...c,notifications:e}}),r.duration&&r.duration>0&&setTimeout(()=>{f(s)},r.duration)}catch(c){return console.error("NotificationStore: 添加通知时发生错误",c),""}return s}function f(i){if(!i||typeof i!="string"){console.warn("NotificationStore: 无效的通知ID",i);return}try{a.update(o=>({...o,notifications:o.notifications.filter(n=>n.id!==i)}))}catch(o){console.error("NotificationStore: 移除通知时发生错误",o)}}function N(){try{a.update(i=>({...i,notifications:[]}))}catch(i){console.error("NotificationStore: 清除所有通知时发生错误",i)}}const b=(i,o)=>t("success",i,o),m=(i,o)=>t("error",i,o),w=(i,o)=>t("warning",i,o),S=(i,o)=>t("info",i,o),x={subscribe:a.subscribe,addNotification:t,removeNotification:f,clearAllNotifications:N,success:b,error:m,warning:w,info:S};export{x as n};
