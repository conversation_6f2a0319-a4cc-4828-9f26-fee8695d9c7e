import{l as o,m as _}from"./B9CKrN7X.js";import{l as d}from"./Cc0s-Eqn.js";function f(e,u,r){if(e.multiple)return c(e,u);for(var n of e.options){var t=i(n);if(_(t,u)){n.selected=!0;return}}(!r||u!==void 0)&&(e.selectedIndex=-1)}function s(e,u){o(()=>{var r=new MutationObserver(()=>{var n=e.__value;f(e,n)});return r.observe(e,{childList:!0,subtree:!0,attributes:!0,attributeFilter:["value"]}),()=>{r.disconnect()}})}function p(e,u,r=u){var n=!0;d(e,"change",t=>{var a=t?"[selected]":":checked",l;if(e.multiple)l=[].map.call(e.querySelectorAll(a),i);else{var v=e.querySelector(a)??e.querySelector("option:not([disabled])");l=v&&i(v)}r(l)}),o(()=>{var t=u();if(f(e,t,n),n&&t===void 0){var a=e.querySelector(":checked");a!==null&&(t=i(a),r(t))}e.__value=t,n=!1}),s(e)}function c(e,u){for(var r of e.options)r.selected=~u.indexOf(i(r))}function i(e){return"__value"in e?e.__value:e.value}export{p as b};
