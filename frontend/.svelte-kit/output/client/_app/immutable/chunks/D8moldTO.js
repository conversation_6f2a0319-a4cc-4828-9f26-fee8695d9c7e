import{ad as da,C as ca,I as H,B as b,ab as _a,D as ra,i as V,X as pa,F as ha,G as X,J as L,N,ap as ya,K as ta,L as ia,M as ma,au as O,as as na,ag as R,av as Y,w as $a,aw as J,ax as z,ay as wa,ah as Ea,az as ga,aA as xa,aB as q,aC as ba,an as Ca,al as Ta,R as sa,aD as fa,aE as Aa,E as Fa,aF as Ia,aG as Na,l as Sa,g as ka,aH as Ra,aI as Ma,v as S,ai as Q,aj as Z,ak as Da}from"./B9CKrN7X.js";import{g as Ha,w as La}from"./Cc0s-Eqn.js";function Xa(e,a){return a}function Oa(e,a,r,i){for(var f=[],s=a.length,o=0;o<s;o++)wa(a[o].e,f,!0);var _=s>0&&f.length===0&&r!==null;if(_){var m=r.parentNode;Ea(m),m.append(r),i.clear(),C(e,a[0].prev,a[s-1].next)}ga(f,()=>{for(var l=0;l<s;l++){var n=a[l];_||(i.delete(n.k),C(e,n.prev,n.next)),xa(n.e,!_)}})}function Ya(e,a,r,i,f,s=null){var o=e,_={flags:a,items:new Map,first:null},m=(a&fa)!==0;if(m){var l=e;o=b?H(_a(l)):l.appendChild(da())}b&&ra();var n=null,$=!1,u=pa(()=>{var v=r();return Ta(v)?v:v==null?[]:na(v)});ca(()=>{var v=V(u),t=v.length;if($&&t===0)return;$=t===0;let c=!1;if(b){var h=o.data===ha;h!==(t===0)&&(o=X(),H(o),L(!1),c=!0)}if(b){for(var p=null,E,w=0;w<t;w++){if(N.nodeType===8&&N.data===ya){o=N,c=!0,L(!1);break}var d=v[w],y=i(d,w);E=oa(N,_,p,null,d,y,w,f,a,r),_.items.set(y,E),p=E}t>0&&H(X())}b||Ba(v,_,o,f,a,i,r),s!==null&&(t===0?n?ta(n):n=ia(()=>s(o)):n!==null&&ma(n,()=>{n=null})),c&&L(!0),V(u)}),b&&(o=N)}function Ba(e,a,r,i,f,s,o){var G,U,W,K;var _=(f&Aa)!==0,m=(f&(q|z))!==0,l=e.length,n=a.items,$=a.first,u=$,v,t=null,c,h=[],p=[],E,w,d,y;if(_)for(y=0;y<l;y+=1)E=e[y],w=s(E,y),d=n.get(w),d!==void 0&&((G=d.a)==null||G.measure(),(c??(c=new Set)).add(d));for(y=0;y<l;y+=1){if(E=e[y],w=s(E,y),d=n.get(w),d===void 0){var F=u?u.e.nodes_start:r;t=oa(F,a,t,t===null?a.first:t.next,E,w,y,i,f,o),n.set(w,t),h=[],p=[],u=t.next;continue}if(m&&za(d,E,y,f),(d.e.f&O)!==0&&(ta(d.e),_&&((U=d.a)==null||U.unfix(),(c??(c=new Set)).delete(d))),d!==u){if(v!==void 0&&v.has(d)){if(h.length<p.length){var T=p[0],g;t=T.prev;var A=h[0],M=h[h.length-1];for(g=0;g<h.length;g+=1)j(h[g],T,r);for(g=0;g<p.length;g+=1)v.delete(p[g]);C(a,A.prev,M.next),C(a,t,A),C(a,M,T),u=T,t=M,y-=1,h=[],p=[]}else v.delete(d),j(d,u,r),C(a,d.prev,d.next),C(a,d,t===null?a.first:t.next),C(a,t,d),t=d;continue}for(h=[],p=[];u!==null&&u.k!==w;)(u.e.f&O)===0&&(v??(v=new Set)).add(u),p.push(u),u=u.next;if(u===null)continue;d=u}h.push(d),t=d,u=d.next}if(u!==null||v!==void 0){for(var I=v===void 0?[]:na(v);u!==null;)(u.e.f&O)===0&&I.push(u),u=u.next;var D=I.length;if(D>0){var va=(f&fa)!==0&&l===0?r:null;if(_){for(y=0;y<D;y+=1)(W=I[y].a)==null||W.measure();for(y=0;y<D;y+=1)(K=I[y].a)==null||K.fix()}Oa(a,I,va,n)}}_&&sa(()=>{var P;if(c!==void 0)for(d of c)(P=d.a)==null||P.apply()}),R.first=a.first&&a.first.e,R.last=t&&t.e}function za(e,a,r,i){(i&q)!==0&&Y(e.v,a),(i&z)!==0?Y(e.i,r):e.i=r}function oa(e,a,r,i,f,s,o,_,m,l){var n=(m&q)!==0,$=(m&ba)===0,u=n?$?$a(f):J(f):f,v=(m&z)===0?o:J(o),t={i:v,v:u,k:s,a:null,e:null,prev:r,next:i};try{return t.e=ia(()=>_(e,u,v,l),b),t.e.prev=r&&r.e,t.e.next=i&&i.e,r===null?a.first=t:(r.next=t,r.e.next=t.e),i!==null&&(i.prev=t,i.e.prev=t.e),t}finally{}}function j(e,a,r){for(var i=e.next?e.next.e.nodes_start:r,f=a?a.e.nodes_start:r,s=e.e.nodes_start;s!==i;){var o=Ca(s);f.before(s),s=o}}function C(e,a,r){a===null?e.first=r:(a.next=r,a.e.next=r&&r.e),r!==null&&(r.prev=a,r.e.prev=a&&a.e)}function Ja(e,a,r,i,f){var _;b&&ra();var s=(_=a.$$slots)==null?void 0:_[r],o=!1;s===!0&&(s=a[r==="default"?"children":r],o=!0),s===void 0?f!==null&&f(e):s(e,o?()=>i:i)}const qa=()=>performance.now(),x={tick:e=>requestAnimationFrame(e),now:()=>qa(),tasks:new Set};function ua(){const e=x.now();x.tasks.forEach(a=>{a.c(e)||(x.tasks.delete(a),a.f())}),x.tasks.size!==0&&x.tick(ua)}function Ga(e){let a;return x.tasks.size===0&&x.tick(ua),{promise:new Promise(r=>{x.tasks.add(a={c:e,f:r})}),abort(){x.tasks.delete(a)}}}function k(e,a){La(()=>{e.dispatchEvent(new CustomEvent(a))})}function Ua(e){if(e==="float")return"cssFloat";if(e==="offset")return"cssOffset";if(e.startsWith("--"))return e;const a=e.split("-");return a.length===1?a[0]:a[0]+a.slice(1).map(r=>r[0].toUpperCase()+r.slice(1)).join("")}function aa(e){const a={},r=e.split(";");for(const i of r){const[f,s]=i.split(":");if(!f||s===void 0)break;const o=Ua(f.trim());a[o]=s.trim()}return a}const Wa=e=>e;function Qa(e,a,r,i){var f=(e&Ra)!==0,s="both",o,_=a.inert,m=a.style.overflow,l,n;function $(){var h=Da,p=R;Q(null),Z(null);try{return o??(o=r()(a,(i==null?void 0:i())??{},{direction:s}))}finally{Q(h),Z(p)}}var u={is_global:f,in(){a.inert=_,k(a,"introstart"),l=B(a,$(),n,1,()=>{k(a,"introend"),l==null||l.abort(),l=o=void 0,a.style.overflow=m})},out(h){a.inert=!0,k(a,"outrostart"),n=B(a,$(),l,0,()=>{k(a,"outroend"),h==null||h()})},stop:()=>{l==null||l.abort(),n==null||n.abort()}},v=R;if((v.transitions??(v.transitions=[])).push(u),Ha){var t=f;if(!t){for(var c=v.parent;c&&(c.f&Fa)!==0;)for(;(c=c.parent)&&(c.f&Ia)===0;);t=!c||(c.f&Na)!==0}t&&Sa(()=>{ka(()=>u.in())})}}function B(e,a,r,i,f){var s=i===1;if(Ma(a)){var o,_=!1;return sa(()=>{if(!_){var h=a({direction:s?"in":"out"});o=B(e,h,r,i,f)}}),{abort:()=>{_=!0,o==null||o.abort()},deactivate:()=>o.deactivate(),reset:()=>o.reset(),t:()=>o.t()}}if(r==null||r.deactivate(),!(a!=null&&a.duration))return f(),{abort:S,deactivate:S,reset:S,t:()=>i};const{delay:m=0,css:l,tick:n,easing:$=Wa}=a;var u=[];if(s&&r===void 0&&(n&&n(0,1),l)){var v=aa(l(0,1));u.push(v,v)}var t=()=>1-i,c=e.animate(u,{duration:m});return c.onfinish=()=>{var h=(r==null?void 0:r.t())??1-i;r==null||r.abort();var p=i-h,E=a.duration*Math.abs(p),w=[];if(E>0){var d=!1;if(l)for(var y=Math.ceil(E/16.666666666666668),F=0;F<=y;F+=1){var T=h+p*$(F/y),g=aa(l(T,1-T));w.push(g),d||(d=g.overflow==="hidden")}d&&(e.style.overflow="hidden"),t=()=>{var A=c.currentTime;return h+p*$(A/E)},n&&Ga(()=>{if(c.playState!=="running")return!1;var A=t();return n(A,1-A),!0})}c=e.animate(w,{duration:E,fill:"forwards"}),c.onfinish=()=>{t=()=>i,n==null||n(i,1-i),f()}},{abort:()=>{c&&(c.cancel(),c.effect=null,c.onfinish=S)},deactivate:()=>{f=S},reset:()=>{i===0&&(n==null||n(1,0))},t:()=>t()}}const Ka=e=>e;function la(e){const a=e-1;return a*a*a+1}function ea(e){const a=typeof e=="string"&&e.match(/^\s*(-?[\d.]+)([^\s]*)\s*$/);return a?[parseFloat(a[1]),a[2]||"px"]:[e,"px"]}function Za(e,{delay:a=0,duration:r=400,easing:i=Ka}={}){const f=+getComputedStyle(e).opacity;return{delay:a,duration:r,easing:i,css:s=>`opacity: ${s*f}`}}function ja(e,{delay:a=0,duration:r=400,easing:i=la,x:f=0,y:s=0,opacity:o=0}={}){const _=getComputedStyle(e),m=+_.opacity,l=_.transform==="none"?"":_.transform,n=m*(1-o),[$,u]=ea(f),[v,t]=ea(s);return{delay:a,duration:r,easing:i,css:(c,h)=>`
			transform: ${l} translate(${(1-c)*$}${u}, ${(1-c)*v}${t});
			opacity: ${m-n*h}`}}function ae(e,{delay:a=0,duration:r=400,easing:i=la,axis:f="y"}={}){const s=getComputedStyle(e),o=+s.opacity,_=f==="y"?"height":"width",m=parseFloat(s[_]),l=f==="y"?["top","bottom"]:["left","right"],n=l.map(p=>`${p[0].toUpperCase()}${p.slice(1)}`),$=parseFloat(s[`padding${n[0]}`]),u=parseFloat(s[`padding${n[1]}`]),v=parseFloat(s[`margin${n[0]}`]),t=parseFloat(s[`margin${n[1]}`]),c=parseFloat(s[`border${n[0]}Width`]),h=parseFloat(s[`border${n[1]}Width`]);return{delay:a,duration:r,easing:i,css:p=>`overflow: hidden;opacity: ${Math.min(p*20,1)*o};${_}: ${p*m}px;padding-${l[0]}: ${p*$}px;padding-${l[1]}: ${p*u}px;margin-${l[0]}: ${p*v}px;margin-${l[1]}: ${p*t}px;border-${l[0]}-width: ${p*c}px;border-${l[1]}-width: ${p*h}px;min-${_}: 0`}}export{Za as a,ae as b,Ya as e,ja as f,Xa as i,Ja as s,Qa as t};
