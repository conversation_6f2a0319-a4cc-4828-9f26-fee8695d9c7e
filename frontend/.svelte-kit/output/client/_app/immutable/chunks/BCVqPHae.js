import{w as s,d}from"./D6jnoBFN.js";const r="app_theme_settings";function n(){{const e=localStorage.getItem(r);if(e)try{return JSON.parse(e)}catch(o){console.error("Failed to parse theme settings from localStorage:",o)}if(window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches)return{darkMode:!0,customBackground:null}}return{darkMode:!1,customBackground:null}}const{subscribe:a,set:u,update:t}=s(n());a(e=>{localStorage.setItem(r,JSON.stringify(e)),e.darkMode?document.documentElement.classList.add("dark"):document.documentElement.classList.remove("dark"),e.customBackground?(document.documentElement.style.setProperty("--custom-background",`url(${e.customBackground})`),document.documentElement.classList.add("has-custom-bg"),document.body.classList.add("has-background"),document.body.style.backgroundColor="transparent",document.documentElement.style.backgroundColor="transparent",e.darkMode?(document.body.style.backgroundColor="transparent",document.documentElement.style.backgroundColor="transparent",document.body.classList.add("dark-with-background")):document.body.classList.remove("dark-with-background")):(document.documentElement.style.setProperty("--custom-background","none"),document.documentElement.classList.remove("has-custom-bg"),document.body.classList.remove("has-background"),document.body.classList.remove("dark-with-background"),document.body.style.backgroundColor="",document.documentElement.style.backgroundColor="")});const c={subscribe:a,set:u,update:t,toggleDarkMode:()=>{t(e=>({...e,darkMode:!e.darkMode}))},setCustomBackground:e=>{t(o=>({...o,customBackground:e}))}},l=d(c,e=>e.darkMode),k=d(c,e=>e.customBackground);export{k as c,l as i,c as t};
