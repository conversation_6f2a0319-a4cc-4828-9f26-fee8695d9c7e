import{a as c}from"./CaTJm4T8.js";import{g as T}from"./D6jnoBFN.js";import"./zBAGAr4e.js";const m="";console.warn("VITE_API_BASE_URL is not defined in your .env file. API calls may fail.");let A=!1,k=[];function p(e,r=null){k.forEach(t=>{e?t.reject(e):r&&l(t.config.endpoint,t.config.method,t.config.body,t.config.requiresAuth,t.config.customHeaders,!0).then(t.resolve).catch(t.reject)}),k=[]}async function l(e,r,t=null,o=!0,i={},w=!1){const f={endpoint:e,method:r,body:t,requiresAuth:o,customHeaders:i,isRetry:w},h=`${m}${e}`,y={"Content-Type":"application/json",...i},g=T(c);o&&g&&g.accessToken?y.Authorization=`Bearer ${g.accessToken}`:o&&(!g||!g.accessToken)&&!A&&console.warn(`Authenticated request to ${h} without an access token and not refreshing.`);const S={method:r,headers:y};t&&(S.body=JSON.stringify(t));try{console.log(`API Request: ${r} ${h}`);const s=await fetch(h,S);if(s.status===308||s.status===307){console.warn(`Redirect detected (${s.status}) for ${h}. This may indicate a URL format issue.`);const u=s.headers.get("Location");u&&console.log(`Redirect URL: ${u}`)}if(s.status===204)return null;let n;const a=s.headers.get("content-type");if(a&&a.includes("application/json")?n=await s.json():n=await s.text()||s.statusText||null,!s.ok){const u=new Error((n==null?void 0:n.message)||s.statusText||"API Request Failed");throw u.response=s,u.status=s.status,u.data=n,console.error(`API Error: ${r} ${h} returned ${s.status} ${s.statusText}`),u}return n}catch(s){const n=f;if(s.status===401&&!n.isRetry&&n.requiresAuth){if(A)return console.log("Token refresh in progress. Queuing request to:",n.endpoint),new Promise((a,u)=>{k.push({resolve:a,reject:u,config:n})});A=!0;try{console.log("Access token expired or invalid. Attempting to refresh...");const a=await _.refreshAccessToken();if(a)return console.log("Token refreshed successfully. Retrying original request and processing queue."),p(null,a),l(n.endpoint,n.method,n.body,n.requiresAuth,n.customHeaders,!0);throw console.error("Failed to refresh token, newAccessToken is null. User should be logged out."),p(s,null),s}catch(a){throw console.error("Error during token refresh process:",a),p(a,null),a}finally{A=!1}}throw console.error(`API Error (${r} ${e}):`,s.status,s.message,s.data||s),s}}const d={get:(e,r=!0,t={})=>l(e,"GET",null,r,t),post:(e,r,t=!0,o={})=>l(e,"POST",r,t,o),put:(e,r,t=!0,o={})=>l(e,"PUT",r,t,o),delete:(e,r=!0,t={})=>l(e,"DELETE",null,r,t),patch:(e,r,t=!0,o={})=>l(e,"PATCH",r,t,o)};async function v(e){try{const r=await d.post("/auth/login",e,!1);if(r.access_token&&r.refresh_token)c.login({access_token:r.access_token,refresh_token:r.refresh_token}),await E();else throw new Error("Login response did not include tokens.")}catch(r){throw console.error("AuthService: Login failed",r),c.logout(),r}}async function P(e){var r,t;try{if(console.log("AuthService: Starting user registration process"),!e.username||!e.email||!e.password)throw console.error("AuthService: Missing required registration fields"),new Error("Missing required fields (username, email, password)");if(e.password.length<8)throw console.error("AuthService: Password too short"),new Error("Password must be at least 8 characters long");console.log("AuthService: Sending registration request to API");const o=await d.post("/auth/register",e,!1);return console.log("AuthService: Registration successful",{userId:(r=o.user)==null?void 0:r.id,username:(t=o.user)==null?void 0:t.username,message:o.message}),o}catch(o){if(console.error("AuthService: Registration failed",o),o instanceof Error){console.error("AuthService: Error details:",{message:o.message,stack:o.stack,name:o.name});const i=o;i.status&&console.error("AuthService: API error details:",{status:i.status,data:i.data})}throw o}}async function E(){try{const e=await d.get("/auth/me");return e?(c.setUserProfile(e),e):null}catch(e){throw console.error("AuthService: Failed to fetch user profile",e),(e.status===401||e.status===422)&&c.logout(),e}}async function R(){try{T(c).accessToken&&await d.post("/auth/logout",{},!0)}catch(e){console.error("AuthService: Server logout failed (access token revocation)",e)}finally{c.logout()}}async function $(e){try{return await d.post("/auth/change-password",e,!0)}catch(r){throw console.error("AuthService: Change password failed",r),r}}async function U(){const e=T(c);if(!e.refreshToken)return console.warn("AuthService: No refresh token available to refresh access token."),c.logout(),null;try{const t=await fetch("/auth/refresh",{method:"POST",headers:{Authorization:`Bearer ${e.refreshToken}`,"Content-Type":"application/json"}});if(!t.ok){const w=await t.json().catch(()=>({message:t.statusText})),f=new Error(w.message||"Token refresh failed");throw f.status=t.status,f.data=w,f}const i=(await t.json()).access_token;if(i)return c.setTokens({accessToken:i}),i;throw new Error("New access token not found in refresh response.")}catch(r){throw console.error("AuthService: Failed to refresh access token",r),(r.status===401||r.status===422)&&c.logout(),r}}const _={loginUser:v,registerUser:P,logoutUser:R,fetchUserProfile:E,changePassword:$,refreshAccessToken:U};export{_ as a,d as b};
