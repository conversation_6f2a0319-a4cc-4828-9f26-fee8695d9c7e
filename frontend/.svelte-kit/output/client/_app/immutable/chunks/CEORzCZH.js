import{ab as d,ac as w,ad as m,ae as p,af as N,ag as h,B as u,N as i,D as y,I as x}from"./B9CKrN7X.js";function g(r){var t=document.createElement("template");return t.innerHTML=r,t.content}function n(r,t){var e=h;e.nodes_start===null&&(e.nodes_start=r,e.nodes_end=t)}function b(r,t){var e=(t&p)!==0,_=(t&N)!==0,s,l=!r.startsWith("<!>");return()=>{if(u)return n(i,null),i;s===void 0&&(s=g(l?r:"<!>"+r),e||(s=d(s)));var a=_||w?document.importNode(s,!0):s.cloneNode(!0);if(e){var c=d(a),o=a.lastChild;n(c,o)}else n(a,a);return a}}function D(r,t,e="svg"){var _=!r.startsWith("<!>"),s=(t&p)!==0,l=`<${e}>${_?r:"<!>"+r}</${e}>`,a;return()=>{if(u)return n(i,null),i;if(!a){var c=g(l),o=d(c);if(s)for(a=document.createDocumentFragment();d(o);)a.appendChild(d(o));else a=d(o)}var f=a.cloneNode(!0);if(s){var E=d(f),T=f.lastChild;n(E,T)}else n(f,f);return f}}function I(r=""){if(!u){var t=m(r+"");return n(t,t),t}var e=i;return e.nodeType!==3&&(e.before(e=m()),x(e)),n(e,e),e}function L(){if(u)return n(i,null),i;var r=document.createDocumentFragment(),t=document.createComment(""),e=m();return r.append(t,e),n(t,e),r}function P(r,t){if(u){h.nodes_end=i,y();return}r!==null&&r.before(t)}const C="5";var v;typeof window<"u"&&((v=window.__svelte??(window.__svelte={})).v??(v.v=new Set)).add(C);export{P as a,n as b,L as c,I as d,D as n,b as t};
