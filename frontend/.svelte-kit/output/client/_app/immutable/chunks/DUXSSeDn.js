import{B as h,J as I,a5 as V,a6 as H,a7 as Y,a8 as q,a9 as z}from"./B9CKrN7X.js";import{i as D,c as G,d as J,a as K,n as j,b as F,f as Q}from"./Cc0s-Eqn.js";import{t as W,c as X,s as Z}from"./BIuBsydj.js";function S(s,a={},i,e){for(var u in i){var c=i[u];a[u]!==c&&(i[u]==null?s.style.removeProperty(u):s.style.setProperty(u,c,e))}}function m(s,a,i,e){var u=s.__style;if(h||u!==a){var c=W(a,e);(!h||c!==s.getAttribute("style"))&&(c==null?s.removeAttribute("style"):s.style.cssText=c),s.__style=a}else e&&(Array.isArray(e)?(S(s,i==null?void 0:i[0],e[0]),S(s,i==null?void 0:i[1],e[1],"important")):S(s,i,e));return e}const n=Symbol("class"),v=Symbol("style"),C=Symbol("is custom element"),$=Symbol("is html");function rs(s){if(h){var a=!1,i=()=>{if(!a){if(a=!0,s.hasAttribute("value")){var e=s.value;b(s,"value",null),s.value=e}if(s.hasAttribute("checked")){var u=s.checked;b(s,"checked",null),s.checked=u}}};s.__on_r=i,q(i),F()}}function ts(s,a){var i=k(s);i.checked!==(i.checked=a??void 0)&&(s.checked=a)}function x(s,a){a?s.hasAttribute("selected")||s.setAttribute("selected",""):s.removeAttribute("selected")}function b(s,a,i,e){var u=k(s);h&&(u[a]=s.getAttribute(a),a==="src"||a==="srcset"||a==="href"&&s.nodeName==="LINK")||u[a]!==(u[a]=i)&&(a==="loading"&&(s[Y]=i),i==null?s.removeAttribute(a):typeof i!="string"&&O(s).includes(a)?s[a]=i:s.setAttribute(a,i))}function es(s,a,i,e,u=!1){var c=k(s),A=c[C],p=!c[$];let N=h&&A;N&&I(!1);var l=a||{},y=s.tagName==="OPTION";for(var L in a)L in i||(i[L]=null);i.class?i.class=X(i.class):i[n]&&(i.class=null),i[v]&&(i.style??(i.style=null));var P=O(s);for(const r in i){let t=i[r];if(y&&r==="value"&&t==null){s.value=s.__value="",l[r]=t;continue}if(r==="class"){var B=s.namespaceURI==="http://www.w3.org/1999/xhtml";Z(s,B,t,e,a==null?void 0:a[n],i[n]),l[r]=t,l[n]=i[n];continue}if(r==="style"){m(s,t,a==null?void 0:a[v],i[v]),l[r]=t,l[v]=i[v];continue}var T=l[r];if(t!==T){l[r]=t;var w=r[0]+r[1];if(w!=="$$")if(w==="on"){const o={},d="$$"+r;let f=r.slice(2);var g=Q(f);if(D(f)&&(f=f.slice(0,-7),o.capture=!0),!g&&T){if(t!=null)continue;s.removeEventListener(f,l[d],o),l[d]=null}if(t!=null)if(g)s[`__${f}`]=t,J([f]);else{let R=function(U){l[r].call(this,U)};l[d]=G(f,s,R,o)}else g&&(s[`__${f}`]=void 0)}else if(r==="style")b(s,r,t);else if(r==="autofocus")K(s,!!t);else if(!A&&(r==="__value"||r==="value"&&t!=null))s.value=s.__value=t;else if(r==="selected"&&y)x(s,t);else{var _=r;p||(_=j(_));var E=_==="defaultValue"||_==="defaultChecked";if(t==null&&!A&&!E)if(c[r]=null,_==="value"||_==="checked"){let o=s;const d=a===void 0;if(_==="value"){let f=o.defaultValue;o.removeAttribute(_),o.defaultValue=f,o.value=o.__value=d?f:null}else{let f=o.defaultChecked;o.removeAttribute(_),o.defaultChecked=f,o.checked=d?f:!1}}else s.removeAttribute(r);else E||P.includes(_)&&(A||typeof t!="string")?s[_]=t:typeof t!="function"&&b(s,_,t)}}}return N&&I(!0),l}function k(s){return s.__attributes??(s.__attributes={[C]:s.nodeName.includes("-"),[$]:s.namespaceURI===V})}var M=new Map;function O(s){var a=M.get(s.nodeName);if(a)return a;M.set(s.nodeName,a=[]);for(var i,e=s,u=Element.prototype;u!==e;){i=z(e);for(var c in i)i[c].set&&a.push(c);e=H(e)}return a}export{es as a,x as b,m as c,ts as d,rs as r,b as s};
