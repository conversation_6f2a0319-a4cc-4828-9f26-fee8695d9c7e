import { E as store_get, F as ensure_array_like, G as attr_class, I as escape_html, J as stringify, K as unsubscribe_stores, D as pop, A as push, M as slot } from "../../chunks/index.js";
/* empty css               */
import "../../chunks/client.js";
import "../../chunks/authStore.js";
import "../../chunks/themeStore.js";
import { n as notificationStore } from "../../chunks/notificationStore.js";
function NotificationContainer($$payload, $$props) {
  push();
  var $$store_subs;
  const notifications = (() => {
    try {
      const state = store_get($$store_subs ??= {}, "$notificationStore", notificationStore);
      return state?.notifications || [];
    } catch (error) {
      console.error("NotificationContainer: 获取通知状态时发生错误", error);
      return [];
    }
  })();
  function getNotificationClasses(type) {
    const baseClasses = "flex items-center justify-between p-4 rounded-lg shadow-lg border max-w-md w-full";
    switch (type) {
      case "success":
        return `${baseClasses} bg-green-100 dark:bg-green-800/80 text-green-900 dark:text-green-100 border-green-300 dark:border-green-600`;
      case "error":
        return `${baseClasses} bg-red-100 dark:bg-red-800/80 text-red-900 dark:text-red-100 border-red-300 dark:border-red-600`;
      case "warning":
        return `${baseClasses} bg-yellow-100 dark:bg-yellow-800/80 text-yellow-900 dark:text-yellow-100 border-yellow-300 dark:border-yellow-600`;
      case "info":
        return `${baseClasses} bg-blue-100 dark:bg-blue-800/80 text-blue-900 dark:text-blue-100 border-blue-300 dark:border-blue-600`;
      default:
        return `${baseClasses} bg-gray-100 dark:bg-gray-800/80 text-gray-900 dark:text-gray-100 border-gray-300 dark:border-gray-600`;
    }
  }
  function getIconClasses(type) {
    switch (type) {
      case "success":
        return "text-green-700 dark:text-green-200";
      case "error":
        return "text-red-700 dark:text-red-200";
      case "warning":
        return "text-yellow-700 dark:text-yellow-200";
      case "info":
        return "text-blue-700 dark:text-blue-200";
      default:
        return "text-gray-700 dark:text-gray-200";
    }
  }
  function getIcon(type) {
    switch (type) {
      case "success":
        return "✓";
      case "error":
        return "✕";
      case "warning":
        return "⚠";
      case "info":
        return "ℹ";
      default:
        return "•";
    }
  }
  const each_array = ensure_array_like(notifications);
  $$payload.out += `<div class="notification-container fixed top-4 left-1/2 transform -translate-x-1/2 flex flex-col gap-2 pointer-events-none"><!--[-->`;
  for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
    let notification = each_array[$$index];
    $$payload.out += `<div${attr_class(`${stringify(getNotificationClasses(notification.type))} pointer-events-auto`)}><div class="flex items-center gap-3"><div${attr_class(`${stringify(getIconClasses(notification.type))} text-lg font-bold`)}>${escape_html(getIcon(notification.type))}</div> <span class="text-sm font-medium flex-1">${escape_html(notification.message)}</span></div> `;
    if (notification.dismissible) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<button type="button" class="ml-3 flex-shrink-0 rounded-full p-1 hover:bg-black/10 dark:hover:bg-white/10 transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-current" aria-label="关闭通知"><svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg></button>`;
    } else {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]--></div>`;
  }
  $$payload.out += `<!--]--></div>`;
  if ($$store_subs) unsubscribe_stores($$store_subs);
  pop();
}
function _layout($$payload, $$props) {
  push();
  $$payload.out += `<!---->`;
  slot($$payload, $$props, "default", {}, null);
  $$payload.out += `<!----> `;
  NotificationContainer($$payload);
  $$payload.out += `<!---->`;
  pop();
}
export {
  _layout as default
};
