import { R as attr, D as pop, A as push } from "../../../chunks/index.js";
import "../../../chunks/client.js";
import "../../../chunks/authService.js";
import "../../../chunks/authStore.js";
function _page($$payload, $$props) {
  push();
  let username = "";
  let email = "";
  let password = "";
  let confirmPassword = "";
  let isLoading = false;
  $$payload.out += `<div class="register-container svelte-12mlfgf"><div class="register-card svelte-12mlfgf"><h1 class="card-title svelte-12mlfgf">Create Account</h1> <p class="card-subtitle svelte-12mlfgf">Join us! Fill in the details below to get started.</p> <form class="register-form svelte-12mlfgf"><div class="form-group svelte-12mlfgf"><label for="username" class="svelte-12mlfgf">Username</label> <input type="text" id="username"${attr("value", username)} required placeholder="your_username"${attr("disabled", isLoading, true)} class="svelte-12mlfgf"></div> <div class="form-group svelte-12mlfgf"><label for="email" class="svelte-12mlfgf">Email Address</label> <input type="email" id="email"${attr("value", email)} required placeholder="<EMAIL>"${attr("disabled", isLoading, true)} class="svelte-12mlfgf"></div> <div class="form-group svelte-12mlfgf"><label for="password" class="svelte-12mlfgf">Password</label> <input type="password" id="password"${attr("value", password)} required placeholder="•••••••• (min. 8 characters)"${attr("disabled", isLoading, true)} class="svelte-12mlfgf"></div> <div class="form-group svelte-12mlfgf"><label for="confirmPassword" class="svelte-12mlfgf">Confirm Password</label> <input type="password" id="confirmPassword"${attr("value", confirmPassword)} required placeholder="••••••••"${attr("disabled", isLoading, true)} class="svelte-12mlfgf"></div> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> <button type="submit" class="submit-button svelte-12mlfgf"${attr("disabled", isLoading, true)}>`;
  {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<span class="svelte-12mlfgf">Sign Up</span>`;
  }
  $$payload.out += `<!--]--></button></form> <div class="alternative-action svelte-12mlfgf"><p class="svelte-12mlfgf">Already have an account? <a href="/login" class="svelte-12mlfgf">Log in here</a></p></div></div></div>`;
  pop();
}
export {
  _page as default
};
