import "clsx";
import { A as push, N as spread_attributes, T as clsx, I as escape_html, D as pop, M as slot, G as attr_class, R as attr, F as ensure_array_like, V as copy_payload, W as assign_payload } from "../../../chunks/index.js";
import "../../../chunks/themeStore.js";
function Alert($$payload, $$props) {
  push();
  let {
    variant = "default",
    title = "",
    dismissible = false,
    icon = true,
    class: className = "",
    children,
    $$slots,
    $$events,
    ...rest
  } = $$props;
  const variantClassesMap = {
    default: "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300 border-gray-200 dark:border-gray-700",
    primary: "bg-primary-50 text-primary-800 dark:bg-primary-900/20 dark:text-primary-300 border-primary-200 dark:border-primary-800",
    secondary: "bg-secondary-50 text-secondary-800 dark:bg-secondary-900/20 dark:text-secondary-300 border-secondary-200 dark:border-secondary-800",
    danger: "bg-danger-50 text-danger-800 dark:bg-danger-900/20 dark:text-danger-300 border-danger-200 dark:border-danger-800",
    success: "bg-success-50 text-success-800 dark:bg-success-900/20 dark:text-success-300 border-success-200 dark:border-success-800",
    warning: "bg-warning-50 text-warning-800 dark:bg-warning-900/20 dark:text-warning-300 border-warning-200 dark:border-warning-800",
    info: "bg-info-50 text-info-800 dark:bg-info-900/20 dark:text-info-300 border-info-200 dark:border-info-800"
  };
  const iconMap = {
    default: "info",
    primary: "info",
    secondary: "info",
    danger: "alert-triangle",
    success: "check-circle",
    warning: "alert-triangle",
    info: "info"
  };
  const variantClasses = variantClassesMap[variant];
  const alertClasses = `
    relative rounded-lg border p-4
    ${variantClasses}
    ${className}
  `;
  const alertIcon = iconMap[variant];
  {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div${spread_attributes(
      {
        class: clsx(alertClasses),
        role: "alert",
        ...rest
      }
    )}><div class="flex">`;
    if (icon) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<div class="flex-shrink-0 mr-3">`;
      if (alertIcon === "info") {
        $$payload.out += "<!--[-->";
        $$payload.out += `<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="12" y1="16" x2="12" y2="12"></line><line x1="12" y1="8" x2="12.01" y2="8"></line></svg>`;
      } else if (alertIcon === "alert-triangle") {
        $$payload.out += "<!--[1-->";
        $$payload.out += `<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path><line x1="12" y1="9" x2="12" y2="13"></line><line x1="12" y1="17" x2="12.01" y2="17"></line></svg>`;
      } else if (alertIcon === "check-circle") {
        $$payload.out += "<!--[2-->";
        $$payload.out += `<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path><polyline points="22 4 12 14.01 9 11.01"></polyline></svg>`;
      } else {
        $$payload.out += "<!--[!-->";
      }
      $$payload.out += `<!--]--></div>`;
    } else {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]--> <div class="flex-1">`;
    if (title) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<h3 class="text-sm font-medium mb-1">${escape_html(title)}</h3>`;
    } else {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]--> <div class="text-sm">${escape_html(children)}</div></div> `;
    if (dismissible) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<button type="button" class="ml-auto -mx-1.5 -my-1.5 bg-transparent text-current p-1.5 inline-flex items-center justify-center rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-transparent" aria-label="Dismiss"><svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg></button>`;
    } else {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]--></div></div>`;
  }
  $$payload.out += `<!--]-->`;
  pop();
}
function Badge($$payload, $$props) {
  let {
    variant = "default",
    size = "md",
    rounded = "full",
    class: className = "",
    children,
    $$slots,
    $$events,
    ...rest
  } = $$props;
  const variantClassesMap = {
    default: "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300",
    primary: "bg-primary-100 text-primary-800 dark:bg-primary-900/30 dark:text-primary-300",
    secondary: "bg-secondary-100 text-secondary-800 dark:bg-secondary-900/30 dark:text-secondary-300",
    danger: "bg-danger-100 text-danger-800 dark:bg-danger-900/30 dark:text-danger-300",
    success: "bg-success-100 text-success-800 dark:bg-success-900/30 dark:text-success-300",
    warning: "bg-warning-100 text-warning-800 dark:bg-warning-900/30 dark:text-warning-300",
    info: "bg-info-100 text-info-800 dark:bg-info-900/30 dark:text-info-300",
    outline: "bg-transparent border border-gray-300 text-gray-700 dark:border-gray-600 dark:text-gray-300"
  };
  const sizeClassesMap = {
    sm: "px-2 py-0.5 text-xs",
    md: "px-2.5 py-0.5 text-xs",
    lg: "px-3 py-1 text-sm"
  };
  const roundedClassesMap = {
    none: "rounded-none",
    sm: "rounded-sm",
    md: "rounded-md",
    lg: "rounded-lg",
    xl: "rounded-xl",
    full: "rounded-full"
  };
  const variantClasses = variantClassesMap[variant];
  const sizeClasses = sizeClassesMap[size];
  const roundedClasses = roundedClassesMap[rounded];
  const badgeClasses = `
    inline-flex items-center font-medium
    ${variantClasses}
    ${sizeClasses}
    ${roundedClasses}
    ${className}
  `;
  $$payload.out += `<span${spread_attributes({ class: clsx(badgeClasses), ...rest })}>${escape_html(children)}</span>`;
}
function Button($$payload, $$props) {
  let {
    variant = "default",
    size = "md",
    type = "button",
    disabled = false,
    loading = false,
    fullWidth = false,
    class: className = "",
    onclick,
    onfocus,
    onblur,
    onmouseenter,
    onmouseleave,
    $$slots,
    $$events,
    ...rest
  } = $$props;
  const variantClassesMap = {
    default: "bg-gray-100 text-gray-900 hover:bg-gray-200 focus:ring-gray-400 dark:bg-gray-700 dark:text-gray-100 dark:hover:bg-gray-600",
    primary: "bg-primary-500 text-white hover:bg-primary-600 focus:ring-primary-400 dark:bg-primary-600 dark:hover:bg-primary-700",
    secondary: "bg-secondary-500 text-white hover:bg-secondary-600 focus:ring-secondary-400 dark:bg-secondary-600 dark:hover:bg-secondary-700",
    danger: "bg-danger-500 text-white hover:bg-danger-600 focus:ring-danger-400 dark:bg-danger-600 dark:hover:bg-danger-700",
    success: "bg-success-500 text-white hover:bg-success-600 focus:ring-success-400 dark:bg-success-600 dark:hover:bg-success-700",
    warning: "bg-warning-500 text-white hover:bg-warning-600 focus:ring-warning-400 dark:bg-warning-600 dark:hover:bg-warning-700",
    info: "bg-info-500 text-white hover:bg-info-600 focus:ring-info-400 dark:bg-info-600 dark:hover:bg-info-700",
    outline: "border border-gray-300 bg-transparent text-gray-700 hover:bg-gray-50 focus:ring-gray-400 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-800",
    ghost: "bg-transparent text-gray-700 hover:bg-gray-100 focus:ring-gray-400 dark:text-gray-300 dark:hover:bg-gray-800"
  };
  const sizeClassesMap = {
    sm: "px-3 py-1.5 text-xs",
    md: "px-4 py-2 text-sm",
    lg: "px-6 py-3 text-base"
  };
  const variantClasses = variantClassesMap[variant];
  const sizeClasses = sizeClassesMap[size];
  const widthClass = fullWidth ? "w-full" : "";
  const buttonClasses = `
    inline-flex items-center justify-center
    font-medium rounded-md
    transition-colors duration-200
    focus:outline-none focus:ring-2 focus:ring-offset-2
    disabled:opacity-50 disabled:cursor-not-allowed
    ${variantClasses}
    ${sizeClasses}
    ${widthClass}
    ${className}
  `;
  $$payload.out += `<button${spread_attributes(
    {
      type,
      class: clsx(buttonClasses),
      disabled: disabled || loading,
      ...rest
    }
  )}>`;
  if (loading) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<span class="mr-2 inline-block animate-spin"><svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 12a9 9 0 1 1-6.219-8.56"></path></svg></span>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> <!---->`;
  slot($$payload, $$props, "default", {}, null);
  $$payload.out += `<!----></button>`;
}
function Card($$payload, $$props) {
  push();
  let {
    variant = "default",
    padding = true,
    shadow = "md",
    rounded = "lg",
    border = true,
    class: className = "",
    children,
    $$slots,
    $$events,
    ...rest
  } = $$props;
  const variantClassesMap = {
    default: "bg-white dark:bg-gray-800",
    primary: "bg-primary-50 dark:bg-primary-900/20",
    secondary: "bg-secondary-50 dark:bg-secondary-900/20",
    danger: "bg-danger-50 dark:bg-danger-900/20",
    success: "bg-success-50 dark:bg-success-900/20",
    warning: "bg-warning-50 dark:bg-warning-900/20",
    info: "bg-info-50 dark:bg-info-900/20"
  };
  const shadowClassesMap = {
    none: "",
    sm: "shadow-sm",
    md: "shadow-md",
    lg: "shadow-lg",
    xl: "shadow-xl"
  };
  const roundedClassesMap = {
    none: "rounded-none",
    sm: "rounded-sm",
    md: "rounded-md",
    lg: "rounded-lg",
    xl: "rounded-xl",
    "2xl": "rounded-2xl",
    "3xl": "rounded-3xl",
    full: "rounded-full"
  };
  const variantClasses = variantClassesMap[variant];
  const shadowClasses = shadowClassesMap[shadow];
  const roundedClasses = roundedClassesMap[rounded];
  const borderClasses = border ? variant === "default" ? "border border-gray-200 dark:border-gray-700" : `border border-${variant}-200 dark:border-${variant}-800` : "";
  const cardClasses = `
    overflow-hidden
    ${variantClasses}
    ${shadowClasses}
    ${roundedClasses}
    ${borderClasses}
    ${className}
  `;
  const bodyPaddingClass = padding ? "p-6" : "";
  $$payload.out += `<div${spread_attributes({ class: clsx(cardClasses), ...rest })}>`;
  if (children?.header) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">${escape_html(children.header)}</div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> <div${attr_class(clsx(bodyPaddingClass))}>${escape_html(children?.default)}</div> `;
  if (children?.footer) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/80">${escape_html(children.footer)}</div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div>`;
  pop();
}
function Input($$payload, $$props) {
  push();
  let {
    type = "text",
    value = "",
    placeholder = "",
    label = "",
    id = "",
    name = "",
    disabled = false,
    readonly = false,
    required = false,
    error = "",
    hint = "",
    fullWidth = true,
    class: className = "",
    onfocus,
    onblur,
    $$slots,
    $$events,
    ...rest
  } = $$props;
  const inputId = id || `input-${Math.random().toString(36).substring(2, 9)}`;
  const inputClasses = `
    block px-3 py-2 bg-white dark:bg-gray-800
    border rounded-md shadow-sm
    focus:outline-none focus:ring-2 focus:ring-offset-0
    disabled:opacity-50 disabled:cursor-not-allowed
    ${error ? "border-danger-500 focus:border-danger-500 focus:ring-danger-400" : "border-gray-300 dark:border-gray-600 focus:border-primary-500 focus:ring-primary-400"}
    ${fullWidth ? "w-full" : ""}
    ${className}
  `;
  if (label) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="mb-4"><label${attr("for", inputId)} class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">${escape_html(label)} `;
    if (required) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<span class="text-danger-500 ml-1">*</span>`;
    } else {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]--></label> <input${spread_attributes(
      {
        type,
        id: inputId,
        name,
        value,
        placeholder,
        disabled,
        readonly,
        required,
        class: clsx(inputClasses),
        ...rest
      }
    )}> `;
    if (error) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<p class="mt-1 text-sm text-danger-500">${escape_html(error)}</p>`;
    } else if (hint) {
      $$payload.out += "<!--[1-->";
      $$payload.out += `<p class="mt-1 text-sm text-gray-500 dark:text-gray-400">${escape_html(hint)}</p>`;
    } else {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]--></div>`;
  } else {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<input${spread_attributes(
      {
        type,
        id: inputId,
        name,
        value,
        placeholder,
        disabled,
        readonly,
        required,
        class: clsx(inputClasses),
        ...rest
      }
    )}>`;
  }
  $$payload.out += `<!--]-->`;
  pop();
}
function Select($$payload, $$props) {
  push();
  let {
    value = "",
    options = [],
    label = "",
    id = "",
    name = "",
    placeholder = "Select an option",
    disabled = false,
    required = false,
    error = "",
    hint = "",
    fullWidth = true,
    class: className = "",
    onfocus,
    onblur,
    $$slots,
    $$events,
    ...rest
  } = $$props;
  const selectId = id || `select-${Math.random().toString(36).substring(2, 9)}`;
  const selectClasses = `
    block px-3 py-2 bg-white dark:bg-gray-800
    border rounded-md shadow-sm
    focus:outline-none focus:ring-2 focus:ring-offset-0
    disabled:opacity-50 disabled:cursor-not-allowed
    ${error ? "border-danger-500 focus:border-danger-500 focus:ring-danger-400" : "border-gray-300 dark:border-gray-600 focus:border-primary-500 focus:ring-primary-400"}
    ${fullWidth ? "w-full" : ""}
    ${className}
  `;
  function isOptionGroup(option) {
    return "options" in option && Array.isArray(option.options);
  }
  if (label) {
    $$payload.out += "<!--[-->";
    const each_array = ensure_array_like(options);
    $$payload.out += `<div class="mb-4"><label${attr("for", selectId)} class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">${escape_html(label)} `;
    if (required) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<span class="text-danger-500 ml-1">*</span>`;
    } else {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]--></label> <div class="relative"><select${spread_attributes(
      {
        id: selectId,
        name,
        disabled,
        required,
        class: clsx(selectClasses),
        ...rest
      }
    )}><option value="" disabled${attr("selected", !value, true)}>${escape_html(placeholder)}</option><!--[-->`;
    for (let $$index_1 = 0, $$length = each_array.length; $$index_1 < $$length; $$index_1++) {
      let option = each_array[$$index_1];
      if (isOptionGroup(option)) {
        $$payload.out += "<!--[-->";
        const each_array_1 = ensure_array_like(option.options);
        $$payload.out += `<optgroup${attr("label", option.label)}${attr("disabled", option.disabled, true)}><!--[-->`;
        for (let $$index = 0, $$length2 = each_array_1.length; $$index < $$length2; $$index++) {
          let subOption = each_array_1[$$index];
          $$payload.out += `<option${attr("value", subOption.value)}${attr("disabled", subOption.disabled, true)}${attr("selected", value === subOption.value, true)}>${escape_html(subOption.label)}</option>`;
        }
        $$payload.out += `<!--]--></optgroup>`;
      } else {
        $$payload.out += "<!--[!-->";
        $$payload.out += `<option${attr("value", option.value)}${attr("disabled", option.disabled, true)}${attr("selected", value === option.value, true)}>${escape_html(option.label)}</option>`;
      }
      $$payload.out += `<!--]-->`;
    }
    $$payload.out += `<!--]--></select> <div class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none"><svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"><path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg></div></div> `;
    if (error) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<p class="mt-1 text-sm text-danger-500">${escape_html(error)}</p>`;
    } else if (hint) {
      $$payload.out += "<!--[1-->";
      $$payload.out += `<p class="mt-1 text-sm text-gray-500 dark:text-gray-400">${escape_html(hint)}</p>`;
    } else {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]--></div>`;
  } else {
    $$payload.out += "<!--[!-->";
    const each_array_2 = ensure_array_like(options);
    $$payload.out += `<div class="relative"><select${spread_attributes(
      {
        id: selectId,
        name,
        disabled,
        required,
        class: clsx(selectClasses),
        ...rest
      }
    )}><option value="" disabled${attr("selected", !value, true)}>${escape_html(placeholder)}</option><!--[-->`;
    for (let $$index_3 = 0, $$length = each_array_2.length; $$index_3 < $$length; $$index_3++) {
      let option = each_array_2[$$index_3];
      if (isOptionGroup(option)) {
        $$payload.out += "<!--[-->";
        const each_array_3 = ensure_array_like(option.options);
        $$payload.out += `<optgroup${attr("label", option.label)}${attr("disabled", option.disabled, true)}><!--[-->`;
        for (let $$index_2 = 0, $$length2 = each_array_3.length; $$index_2 < $$length2; $$index_2++) {
          let subOption = each_array_3[$$index_2];
          $$payload.out += `<option${attr("value", subOption.value)}${attr("disabled", subOption.disabled, true)}${attr("selected", value === subOption.value, true)}>${escape_html(subOption.label)}</option>`;
        }
        $$payload.out += `<!--]--></optgroup>`;
      } else {
        $$payload.out += "<!--[!-->";
        $$payload.out += `<option${attr("value", option.value)}${attr("disabled", option.disabled, true)}${attr("selected", value === option.value, true)}>${escape_html(option.label)}</option>`;
      }
      $$payload.out += `<!--]-->`;
    }
    $$payload.out += `<!--]--></select> <div class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none"><svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"><path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg></div></div>`;
  }
  $$payload.out += `<!--]-->`;
  pop();
}
function _page($$payload) {
  const countries = [
    { value: "us", label: "United States" },
    { value: "ca", label: "Canada" },
    { value: "mx", label: "Mexico" },
    { value: "uk", label: "United Kingdom" },
    { value: "fr", label: "France" }
  ];
  let name = "";
  let email = "";
  let country = "";
  let message = "";
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out += `<div class="max-w-7xl mx-auto px-4 py-12"><header class="mb-12 text-center"><h1 class="text-4xl font-bold mb-4">UI Component Library</h1> <p class="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">A collection of reusable Tailwind CSS components for Svelte applications</p></header> <section class="mb-16"><h2 class="text-2xl font-bold mb-6 pb-2 border-b border-gray-200 dark:border-gray-700">Buttons</h2> <div class="mb-8"><h3 class="text-xl font-semibold mb-4">Variants</h3> <div class="flex flex-wrap gap-4">`;
    Button($$payload2, {
      children: ($$payload3) => {
        $$payload3.out += `<!---->Default`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----> `;
    Button($$payload2, {
      variant: "primary",
      children: ($$payload3) => {
        $$payload3.out += `<!---->Primary`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----> `;
    Button($$payload2, {
      variant: "secondary",
      children: ($$payload3) => {
        $$payload3.out += `<!---->Secondary`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----> `;
    Button($$payload2, {
      variant: "success",
      children: ($$payload3) => {
        $$payload3.out += `<!---->Success`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----> `;
    Button($$payload2, {
      variant: "danger",
      children: ($$payload3) => {
        $$payload3.out += `<!---->Danger`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----> `;
    Button($$payload2, {
      variant: "warning",
      children: ($$payload3) => {
        $$payload3.out += `<!---->Warning`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----> `;
    Button($$payload2, {
      variant: "info",
      children: ($$payload3) => {
        $$payload3.out += `<!---->Info`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----> `;
    Button($$payload2, {
      variant: "outline",
      children: ($$payload3) => {
        $$payload3.out += `<!---->Outline`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----> `;
    Button($$payload2, {
      variant: "ghost",
      children: ($$payload3) => {
        $$payload3.out += `<!---->Ghost`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----></div></div> <div class="mb-8"><h3 class="text-xl font-semibold mb-4">Sizes</h3> <div class="flex flex-wrap items-center gap-4">`;
    Button($$payload2, {
      variant: "primary",
      size: "sm",
      children: ($$payload3) => {
        $$payload3.out += `<!---->Small`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----> `;
    Button($$payload2, {
      variant: "primary",
      size: "md",
      children: ($$payload3) => {
        $$payload3.out += `<!---->Medium`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----> `;
    Button($$payload2, {
      variant: "primary",
      size: "lg",
      children: ($$payload3) => {
        $$payload3.out += `<!---->Large`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----></div></div> <div class="mb-8"><h3 class="text-xl font-semibold mb-4">States</h3> <div class="flex flex-wrap gap-4">`;
    Button($$payload2, {
      variant: "primary",
      disabled: true,
      children: ($$payload3) => {
        $$payload3.out += `<!---->Disabled`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----> `;
    Button($$payload2, {
      variant: "primary",
      loading: true,
      children: ($$payload3) => {
        $$payload3.out += `<!---->Loading`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----> `;
    Button($$payload2, {
      variant: "primary",
      fullWidth: true,
      children: ($$payload3) => {
        $$payload3.out += `<!---->Full Width`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----></div></div></section> <section class="mb-16"><h2 class="text-2xl font-bold mb-6 pb-2 border-b border-gray-200 dark:border-gray-700">Badges</h2> <div class="mb-8"><h3 class="text-xl font-semibold mb-4">Variants</h3> <div class="flex flex-wrap gap-4">`;
    Badge($$payload2, {
      children: ($$payload3) => {
        $$payload3.out += `<!---->Default`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----> `;
    Badge($$payload2, {
      variant: "primary",
      children: ($$payload3) => {
        $$payload3.out += `<!---->Primary`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----> `;
    Badge($$payload2, {
      variant: "secondary",
      children: ($$payload3) => {
        $$payload3.out += `<!---->Secondary`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----> `;
    Badge($$payload2, {
      variant: "success",
      children: ($$payload3) => {
        $$payload3.out += `<!---->Success`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----> `;
    Badge($$payload2, {
      variant: "danger",
      children: ($$payload3) => {
        $$payload3.out += `<!---->Danger`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----> `;
    Badge($$payload2, {
      variant: "warning",
      children: ($$payload3) => {
        $$payload3.out += `<!---->Warning`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----> `;
    Badge($$payload2, {
      variant: "info",
      children: ($$payload3) => {
        $$payload3.out += `<!---->Info`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----> `;
    Badge($$payload2, {
      variant: "outline",
      children: ($$payload3) => {
        $$payload3.out += `<!---->Outline`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----></div></div> <div class="mb-8"><h3 class="text-xl font-semibold mb-4">Sizes</h3> <div class="flex flex-wrap items-center gap-4">`;
    Badge($$payload2, {
      variant: "primary",
      size: "sm",
      children: ($$payload3) => {
        $$payload3.out += `<!---->Small`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----> `;
    Badge($$payload2, {
      variant: "primary",
      size: "md",
      children: ($$payload3) => {
        $$payload3.out += `<!---->Medium`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----> `;
    Badge($$payload2, {
      variant: "primary",
      size: "lg",
      children: ($$payload3) => {
        $$payload3.out += `<!---->Large`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----></div></div></section> <section class="mb-16"><h2 class="text-2xl font-bold mb-6 pb-2 border-b border-gray-200 dark:border-gray-700">Cards</h2> <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">`;
    Card($$payload2, {
      children: ($$payload3) => {
        $$payload3.out += `<p>Simple card with just body content</p>`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----> `;
    Card($$payload2, {
      children: ($$payload3) => {
        $$payload3.out += `<p>Card with header and body content</p>`;
      },
      $$slots: {
        default: true,
        header: ($$payload3) => {
          {
            $$payload3.out += `<h3 class="text-lg font-semibold">Card Title</h3>`;
          }
        }
      }
    });
    $$payload2.out += `<!----> `;
    Card($$payload2, {
      children: ($$payload3) => {
        $$payload3.out += `<p class="mb-4">Card with header, body, and footer</p>`;
      },
      $$slots: {
        default: true,
        header: ($$payload3) => {
          {
            $$payload3.out += `<h3 class="text-lg font-semibold">Complete Card</h3>`;
          }
        },
        footer: ($$payload3) => {
          {
            $$payload3.out += `<div class="flex justify-end">`;
            Button($$payload3, {
              variant: "primary",
              children: ($$payload4) => {
                $$payload4.out += `<!---->Action`;
              },
              $$slots: { default: true }
            });
            $$payload3.out += `<!----></div>`;
          }
        }
      }
    });
    $$payload2.out += `<!----> `;
    Card($$payload2, {
      variant: "primary",
      children: ($$payload3) => {
        $$payload3.out += `<p>Card with primary variant</p>`;
      },
      $$slots: {
        default: true,
        header: ($$payload3) => {
          {
            $$payload3.out += `<h3 class="text-lg font-semibold">Primary Card</h3>`;
          }
        }
      }
    });
    $$payload2.out += `<!----> `;
    Card($$payload2, {
      variant: "success",
      shadow: "lg",
      children: ($$payload3) => {
        $$payload3.out += `<p>Card with success variant and large shadow</p>`;
      },
      $$slots: {
        default: true,
        header: ($$payload3) => {
          {
            $$payload3.out += `<h3 class="text-lg font-semibold">Success Card</h3>`;
          }
        }
      }
    });
    $$payload2.out += `<!----></div></section> <section class="mb-16"><h2 class="text-2xl font-bold mb-6 pb-2 border-b border-gray-200 dark:border-gray-700">Alerts</h2> <div class="space-y-4">`;
    Alert($$payload2, {
      children: ($$payload3) => {
        $$payload3.out += `<!---->Default alert message`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----> `;
    Alert($$payload2, {
      variant: "primary",
      children: ($$payload3) => {
        $$payload3.out += `<!---->Primary alert message`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----> `;
    Alert($$payload2, {
      variant: "success",
      title: "Success",
      children: ($$payload3) => {
        $$payload3.out += `<!---->Operation completed successfully`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----> `;
    Alert($$payload2, {
      variant: "danger",
      title: "Error",
      dismissible: true,
      children: ($$payload3) => {
        $$payload3.out += `<!---->Something went wrong. Please try again.`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----> `;
    Alert($$payload2, {
      variant: "warning",
      title: "Warning",
      dismissible: true,
      children: ($$payload3) => {
        $$payload3.out += `<!---->Your account is about to expire`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----> `;
    Alert($$payload2, {
      variant: "info",
      title: "Information",
      children: ($$payload3) => {
        $$payload3.out += `<!---->This is an informational message`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----></div></section> <section class="mb-16"><h2 class="text-2xl font-bold mb-6 pb-2 border-b border-gray-200 dark:border-gray-700">Form Elements</h2> `;
    Card($$payload2, {
      children: ($$payload3) => {
        $$payload3.out += `<form class="space-y-4">`;
        Input($$payload3, {
          label: "Name",
          type: "text",
          placeholder: "Enter your name",
          required: true,
          get value() {
            return name;
          },
          set value($$value) {
            name = $$value;
            $$settled = false;
          }
        });
        $$payload3.out += `<!----> `;
        Input($$payload3, {
          label: "Email",
          type: "email",
          placeholder: "Enter your email",
          required: true,
          get value() {
            return email;
          },
          set value($$value) {
            email = $$value;
            $$settled = false;
          }
        });
        $$payload3.out += `<!----> `;
        Select($$payload3, {
          label: "Country",
          options: countries,
          placeholder: "Select your country",
          get value() {
            return country;
          },
          set value($$value) {
            country = $$value;
            $$settled = false;
          }
        });
        $$payload3.out += `<!----> `;
        Input($$payload3, {
          label: "Message",
          type: "textarea",
          placeholder: "Enter your message",
          required: true,
          get value() {
            return message;
          },
          set value($$value) {
            message = $$value;
            $$settled = false;
          }
        });
        $$payload3.out += `<!----> <div class="flex justify-end">`;
        Button($$payload3, {
          variant: "primary",
          type: "submit",
          children: ($$payload4) => {
            $$payload4.out += `<!---->Submit`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!----></div></form> `;
        {
          $$payload3.out += "<!--[!-->";
        }
        $$payload3.out += `<!--]-->`;
      },
      $$slots: {
        default: true,
        header: ($$payload3) => {
          {
            $$payload3.out += `<h3 class="text-lg font-semibold">Contact Form Example</h3>`;
          }
        }
      }
    });
    $$payload2.out += `<!----></section></div>`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
}
export {
  _page as default
};
