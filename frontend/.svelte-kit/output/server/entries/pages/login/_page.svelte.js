import { R as attr, D as pop, A as push } from "../../../chunks/index.js";
import "../../../chunks/client.js";
import "../../../chunks/authService.js";
import "../../../chunks/authStore.js";
function _page($$payload, $$props) {
  push();
  let email = "";
  let password = "";
  let isLoading = false;
  $$payload.out += `<div class="login-container svelte-1ds17bf"><div class="login-card svelte-1ds17bf"><h1 class="card-title svelte-1ds17bf">Login</h1> <p class="card-subtitle svelte-1ds17bf">Welcome back! Please enter your credentials.</p> <form class="login-form svelte-1ds17bf"><div class="form-group svelte-1ds17bf"><label for="email" class="svelte-1ds17bf">Email Address</label> <input type="email" id="email"${attr("value", email)} required placeholder="<EMAIL>"${attr("disabled", isLoading, true)} class="svelte-1ds17bf"></div> <div class="form-group svelte-1ds17bf"><label for="password" class="svelte-1ds17bf">Password</label> <input type="password" id="password"${attr("value", password)} required placeholder="••••••••"${attr("disabled", isLoading, true)} class="svelte-1ds17bf"></div> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> <button type="submit" class="submit-button svelte-1ds17bf"${attr("disabled", isLoading, true)}>`;
  {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<span class="svelte-1ds17bf">Login</span>`;
  }
  $$payload.out += `<!--]--></button></form> <div class="alternative-action svelte-1ds17bf"><p class="svelte-1ds17bf">Don't have an account? <a href="/register" class="svelte-1ds17bf">Sign up here</a></p></div></div></div>`;
  pop();
}
export {
  _page as default
};
