import "clsx";
import { D as pop, A as push } from "../../../../chunks/index.js";
import { w as writable } from "../../../../chunks/index2.js";
import "../../../../chunks/authService.js";
const initialIdentityProfileState = {
  profile: null,
  isLoading: false,
  error: null
};
const initialAnchorStoreState = {
  identityProfile: initialIdentityProfileState
  // currentFocus: initialCurrentFocusState, // REMOVED
  // Initialize other sections here when added
};
const { subscribe, set, update } = writable(initialAnchorStoreState);
function _page($$payload, $$props) {
  push();
  $$payload.out += `<div class="max-w-4xl mx-auto px-6 py-8">`;
  {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="text-center py-12"><div class="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-500 mb-4"></div> <p class="text-gray-600 dark:text-gray-400">Loading your identity anchor...</p></div>`;
  }
  $$payload.out += `<!--]--></div>`;
  pop();
}
export {
  _page as default
};
