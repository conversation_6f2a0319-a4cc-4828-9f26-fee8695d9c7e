import { Q as current_component, R as attr, G as attr_class, I as escape_html, M as slot, D as pop, A as push, J as stringify, E as store_get, F as ensure_array_like, K as unsubscribe_stores, S as bind_props, T as clsx } from "../../../../chunks/index.js";
import "../../../../chunks/authStore.js";
import { d as derived, w as writable, g as get } from "../../../../chunks/index2.js";
import { a as api } from "../../../../chunks/authService.js";
import "../../../../chunks/notificationStore.js";
import { c as combineClasses, p as pageContainer, l as layouts, a as columnSpans, b as colorSchemes, d as cardBase, h as headings, s as scrollArea } from "../../../../chunks/pageStyles.js";
function onDestroy(fn) {
  var context = (
    /** @type {Component} */
    current_component
  );
  (context.d ??= []).push(fn);
}
async function getAllTodos() {
  try {
    const response = await api.get("/todo/todos");
    return response.data || [];
  } catch (error) {
    console.error("TodoService: Failed to fetch all todos", error);
    throw error;
  }
}
async function createTodo(payload) {
  try {
    const response = await api.post("/todo/todos", payload);
    return response.data;
  } catch (error) {
    console.error("TodoService: Failed to create todo", error);
    throw error;
  }
}
async function getTodoById(todoId) {
  try {
    const response = await api.get(`/todo/todos/${todoId}`);
    return response.data;
  } catch (error) {
    if (error.status === 404) {
      console.warn(`TodoService: Todo item with ID ${todoId} not found.`);
      return null;
    }
    console.error(`TodoService: Failed to fetch todo with ID ${todoId}`, error);
    throw error;
  }
}
async function updateTodo(todoId, payload) {
  try {
    const response = await api.put(`/todo/todos/${todoId}`, payload);
    return response;
  } catch (error) {
    console.error(`TodoService: Failed to update todo with ID ${todoId}`, error);
    throw error;
  }
}
async function deleteTodo(todoId) {
  try {
    await api.delete(`/todo/todos/${todoId}`);
  } catch (error) {
    console.error(`TodoService: Failed to delete todo with ID ${todoId}`, error);
    throw error;
  }
}
const todoService = {
  getAllTodos,
  createTodo,
  getTodoById,
  updateTodo,
  deleteTodo
};
const initialState = {
  todos: [],
  isLoading: false,
  error: null,
  maxFocusItems: 3
};
const mainTodoWritable = writable(initialState);
let updateCounter = 0;
const forceUpdate = () => {
  updateCounter++;
  mainTodoWritable.update((state) => ({ ...state, updateCounter }));
};
async function loadAllTodos() {
  mainTodoWritable.update((state) => ({ ...state, isLoading: true, error: null }));
  try {
    const fetchedTodos = await todoService.getAllTodos();
    const sortedTodos = fetchedTodos.sort((a, b) => {
      if (a.is_current_focus && !b.is_current_focus) return -1;
      if (!a.is_current_focus && b.is_current_focus) return 1;
      if (a.status !== "completed" && b.status === "completed") return -1;
      if (a.status === "completed" && b.status !== "completed") return 1;
      return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
    });
    mainTodoWritable.set({
      ...get(mainTodoWritable),
      todos: sortedTodos,
      isLoading: false,
      error: null
    });
  } catch (err) {
    const error = err;
    console.error("TodoStore: Error fetching todos", error);
    mainTodoWritable.set({
      ...get(mainTodoWritable),
      todos: [],
      isLoading: false,
      error: error.message || "Failed to fetch todos"
    });
  }
}
async function addTodo(payload) {
  mainTodoWritable.update((state) => ({ ...state, isLoading: true, error: null }));
  try {
    const newTodo = await todoService.createTodo(payload);
    mainTodoWritable.update((state) => {
      const newTodos = [newTodo, ...state.todos].sort((a, b) => {
        if (a.is_current_focus && !b.is_current_focus) return -1;
        if (!a.is_current_focus && b.is_current_focus) return 1;
        return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
      });
      return {
        ...state,
        todos: newTodos,
        isLoading: false,
        error: null
      };
    });
    forceUpdate();
    try {
      await loadAllTodos();
    } catch (reloadError) {
      console.warn("TodoStore: Failed to reload todos after add, but add was successful:", reloadError);
    }
    return newTodo;
  } catch (err) {
    const error = err;
    console.error("TodoStore: Error adding todo", error);
    mainTodoWritable.update((state) => ({ ...state, isLoading: false, error: error.message || "Failed to add todo" }));
    return null;
  }
}
async function editTodo(todoId, payload) {
  mainTodoWritable.update((state) => ({ ...state, isLoading: true, error: null }));
  try {
    const updatedTodo = await todoService.updateTodo(todoId, payload);
    mainTodoWritable.update((state) => {
      const newTodos = state.todos.map((todo) => todo && todo.id === todoId ? updatedTodo : todo);
      return {
        ...state,
        todos: newTodos,
        isLoading: false,
        error: null
      };
    });
    forceUpdate();
    try {
      await loadAllTodos();
    } catch (reloadError) {
      console.warn("TodoStore: Failed to reload todos after edit, but edit was successful:", reloadError);
    }
    return updatedTodo;
  } catch (err) {
    const error = err;
    console.error("TodoStore: Error editing todo", error);
    mainTodoWritable.update((state) => ({ ...state, isLoading: false, error: error.message || `Failed to update todo ${todoId}` }));
    return null;
  }
}
async function toggleCurrentFocus(todoId) {
  const currentState = get(mainTodoWritable);
  if (!currentState || !currentState.todos) {
    console.error("TodoStore: Invalid state or todos array not found");
    return null;
  }
  const targetTodo = currentState.todos.find((t) => t && t.id === todoId);
  if (!targetTodo) {
    console.error(`TodoStore: Todo item with ID ${todoId} not found for toggling focus.`);
    mainTodoWritable.update((state) => ({ ...state, isLoading: false, error: `Todo item ${todoId} not found.` }));
    return null;
  }
  const currentlyFocusedItems = currentState.todos.filter((t) => t && t.is_current_focus && t.id !== todoId && t.status !== "completed");
  const newFocusState = !targetTodo.is_current_focus;
  if (newFocusState && currentlyFocusedItems.length >= currentState.maxFocusItems) {
    const message = `Cannot set more than ${currentState.maxFocusItems} items as current focus. Please remove another item from focus first.`;
    console.warn(message);
    mainTodoWritable.update((state) => ({ ...state, isLoading: false, error: message }));
    return null;
  }
  mainTodoWritable.update((state) => ({ ...state, isLoading: true, error: null }));
  try {
    const updatedTodo = await todoService.updateTodo(todoId, { is_current_focus: newFocusState });
    mainTodoWritable.update((state) => {
      const newTodos = state.todos.map((todo) => todo && todo.id === todoId ? updatedTodo : todo);
      return {
        ...state,
        todos: newTodos,
        isLoading: false,
        error: null
      };
    });
    forceUpdate();
    try {
      await loadAllTodos();
    } catch (reloadError) {
      console.warn("TodoStore: Failed to reload todos after toggle focus, but toggle was successful:", reloadError);
    }
    return updatedTodo;
  } catch (err) {
    const error = err;
    console.error("TodoStore: Error toggling current focus", error);
    mainTodoWritable.update((state) => ({ ...state, isLoading: false, error: error.message || `Failed to toggle focus for item ${todoId}.` }));
    return null;
  }
}
async function removeTodo(todoId) {
  mainTodoWritable.update((state) => ({ ...state, isLoading: true, error: null }));
  try {
    await todoService.deleteTodo(todoId);
    mainTodoWritable.update((state) => {
      const newTodos = state.todos.filter((todo) => todo && todo.id !== todoId);
      return {
        ...state,
        todos: newTodos,
        isLoading: false,
        error: null
      };
    });
    forceUpdate();
    try {
      await loadAllTodos();
    } catch (reloadError) {
      console.warn("TodoStore: Failed to reload todos after remove, but remove was successful:", reloadError);
    }
  } catch (err) {
    const error = err;
    console.error("TodoStore: Error removing todo", error);
    mainTodoWritable.update((state) => ({ ...state, isLoading: false, error: error.message || `Failed to delete todo ${todoId}` }));
  }
}
async function toggleCompleteStatus(todoId, currentItemStatus) {
  const newStatus = currentItemStatus === "completed" ? "pending" : "completed";
  const payload = { status: newStatus };
  const currentState = get(mainTodoWritable);
  if (currentState && currentState.todos) {
    const currentItem = currentState.todos.find((t) => t && t.id === todoId);
    if (newStatus === "completed" && currentItem?.is_current_focus) {
      payload.is_current_focus = false;
    }
  }
  return editTodo(todoId, payload);
}
const currentFocusTodos = derived(
  mainTodoWritable,
  ($mainTodoState) => {
    if (!$mainTodoState || !$mainTodoState.todos) {
      return [];
    }
    const filtered = $mainTodoState.todos.filter((todo) => todo && todo.is_current_focus && todo.status !== "completed").sort((a, b) => new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime());
    return [...filtered];
  }
);
const otherActiveTodos = derived(
  mainTodoWritable,
  ($mainTodoState) => {
    if (!$mainTodoState || !$mainTodoState.todos) {
      return [];
    }
    const filtered = $mainTodoState.todos.filter((todo) => todo && !todo.is_current_focus && todo.status !== "completed").sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
    return [...filtered];
  }
);
const completedTodos = derived(
  mainTodoWritable,
  ($mainTodoState) => {
    if (!$mainTodoState || !$mainTodoState.todos) {
      return [];
    }
    const filtered = $mainTodoState.todos.filter((todo) => todo && todo.status === "completed").sort((a, b) => {
      const dateA = a.completed_at ? new Date(a.completed_at).getTime() : 0;
      const dateB = b.completed_at ? new Date(b.completed_at).getTime() : 0;
      return dateB - dateA;
    });
    return [...filtered];
  }
);
const todoStore = {
  subscribe: mainTodoWritable.subscribe,
  set: mainTodoWritable.set,
  update: mainTodoWritable.update,
  loadAllTodos,
  addTodo,
  editTodo,
  removeTodo,
  toggleCompleteStatus,
  toggleCurrentFocus
};
function Modal($$payload, $$props) {
  push();
  let {
    isOpen = false,
    closeOnBackdropClick = true,
    title = null,
    modalWidth = "max-w-lg",
    close = () => {
    }
  } = $$props;
  let modalContentElement = null;
  function closeModal() {
    close();
  }
  function handleKeydown(event) {
    if (event.key === "Escape" && isOpen) {
      closeModal();
    }
    if (event.key === "Tab" && isOpen && modalContentElement) ;
  }
  onDestroy(() => {
    window.removeEventListener("keydown", handleKeydown);
    if (typeof document !== "undefined") {
      document.body.classList.remove("overflow-hidden-modal");
    }
  });
  if (isOpen) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="fixed inset-0 bg-black/60 flex justify-center items-center p-4 z-modal" role="dialog" tabindex="-1" aria-modal="true"${attr("aria-labelledby", title ? "modal-title" : void 0)}><div${attr_class(`bg-white dark:bg-gray-800 rounded-lg shadow-xl flex flex-col w-full max-h-[90vh] overflow-hidden ${stringify(modalWidth)}`)} role="document" tabindex="-1"${attr("aria-labelledby", title ? "modal-title" : void 0)}><header class="flex justify-between items-center p-4 border-b border-gray-200 dark:border-gray-700 flex-shrink-0">`;
    if (title) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<h2 id="modal-title" class="text-xl font-semibold text-gray-900 dark:text-white m-0">${escape_html(title)}</h2>`;
    } else {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]--> <button class="bg-transparent border-none text-3xl font-light text-gray-500 dark:text-gray-400 cursor-pointer p-1 leading-none opacity-70 hover:opacity-100 transition-opacity" aria-label="Close modal">×</button></header> <main class="p-6 overflow-y-auto flex-grow"><!---->`;
    slot($$payload, $$props, "default", {}, () => {
      $$payload.out += `<p class="text-gray-700 dark:text-gray-300">This is the modal body. Pass content to override this.</p>`;
    });
    $$payload.out += `<!----></main> <!---->`;
    slot($$payload, $$props, "footer", {}, null);
    $$payload.out += `<!----></div></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]-->`;
  pop();
}
function TodoAddModal($$payload, $$props) {
  push();
  let {
    isOpen = false,
    isLoading: externalIsLoading = false,
    onCloseRequest = () => {
    }
  } = $$props;
  let isLoading = externalIsLoading;
  let title = "";
  let description = "";
  let dueDate = "";
  let isCurrentFocus = false;
  let errorMessage = "";
  let successMessage = "";
  let formId = `todo-add-form-${Math.random().toString(36).substring(2)}`;
  function resetForm() {
    title = "";
    description = "";
    dueDate = "";
    isCurrentFocus = false;
    errorMessage = "";
    successMessage = "";
  }
  function handleCancel() {
    resetForm();
    onCloseRequest();
  }
  onDestroy(() => {
    document.removeEventListener("keydown", handleKeyDown);
  });
  function handleKeyDown(event) {
    if (event.key === "Escape" && isOpen) {
      handleCancel();
    }
  }
  if (isOpen) {
    $$payload.out += "<!--[-->";
    Modal($$payload, {
      isOpen,
      close: handleCancel,
      title: "添加待办事项",
      modalWidth: "max-w-xl",
      children: ($$payload2) => {
        $$payload2.out += `<form${attr("id", formId)} class="todo-add-form">`;
        if (errorMessage) {
          $$payload2.out += "<!--[-->";
          $$payload2.out += `<div class="mb-4 p-3 bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 border border-red-200 dark:border-red-800 rounded-md">${escape_html(errorMessage)}</div>`;
        } else {
          $$payload2.out += "<!--[!-->";
        }
        $$payload2.out += `<!--]--> `;
        if (successMessage) {
          $$payload2.out += "<!--[-->";
          $$payload2.out += `<div class="mb-4 p-3 bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300 border border-green-200 dark:border-green-800 rounded-md">${escape_html(successMessage)}</div>`;
        } else {
          $$payload2.out += "<!--[!-->";
        }
        $$payload2.out += `<!--]--> <div class="form-group mb-4"><label for="add-todo-title" class="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300"><span class="label-text">标题</span> <span class="text-red-500 ml-0.5">*</span></label> <input type="text" id="add-todo-title"${attr("value", title)} placeholder="输入待办事项标题" required${attr("disabled", isLoading, true)} class="w-full px-4 py-3 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 disabled:bg-gray-200 dark:disabled:bg-gray-800 disabled:opacity-70 disabled:cursor-not-allowed transition-colors"></div> <div class="form-group mb-4"><label for="add-todo-description" class="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300"><span class="label-text">描述</span></label> <textarea id="add-todo-description" placeholder="添加详细描述..." rows="3"${attr("disabled", isLoading, true)} class="w-full px-4 py-3 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 disabled:bg-gray-200 dark:disabled:bg-gray-800 disabled:opacity-70 disabled:cursor-not-allowed transition-colors">`;
        const $$body = escape_html(description);
        if ($$body) {
          $$payload2.out += `${$$body}`;
        }
        $$payload2.out += `</textarea></div> <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4"><div class="form-group"><label for="add-todo-due-date" class="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300"><span class="label-text">截止日期</span></label> <input type="date" id="add-todo-due-date"${attr("value", dueDate)}${attr("disabled", isLoading, true)} class="w-full px-4 py-3 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 disabled:bg-gray-200 dark:disabled:bg-gray-800 disabled:opacity-70 disabled:cursor-not-allowed transition-colors"></div> <div class="form-group"><label for="add-todo-priority" class="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300"><span class="label-text">优先级</span></label> <select id="add-todo-priority"${attr("disabled", isLoading, true)} class="w-full px-4 py-3 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 disabled:bg-gray-200 dark:disabled:bg-gray-800 disabled:opacity-70 disabled:cursor-not-allowed transition-colors"><option value="low">低</option><option value="medium">中</option><option value="high">高</option></select></div></div> <div class="form-group mb-6"><label class="flex items-center cursor-pointer"><input type="checkbox"${attr("checked", isCurrentFocus, true)}${attr("disabled", isLoading, true)} class="form-checkbox h-5 w-5 text-primary-500 rounded border-gray-300 dark:border-gray-600 focus:ring-primary-500 dark:focus:ring-primary-600 dark:bg-gray-700 disabled:opacity-70 disabled:cursor-not-allowed"> <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">设为当前焦点</span></label></div> <div class="flex justify-end space-x-3"><button type="button"${attr("disabled", isLoading, true)} class="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-400 dark:focus:ring-gray-500 disabled:opacity-70 disabled:cursor-not-allowed transition-colors">取消</button> <button type="submit"${attr("disabled", isLoading, true)} class="px-4 py-2 bg-primary-500 text-white rounded-md hover:bg-primary-600 focus:outline-none focus:ring-2 focus:ring-primary-400 disabled:opacity-70 disabled:cursor-not-allowed transition-colors flex items-center">`;
        if (isLoading) {
          $$payload2.out += "<!--[-->";
          $$payload2.out += `<svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg> 处理中...`;
        } else {
          $$payload2.out += "<!--[!-->";
          $$payload2.out += `添加`;
        }
        $$payload2.out += `<!--]--></button></div></form>`;
      },
      $$slots: { default: true }
    });
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]-->`;
  pop();
}
function TodoListSidebar($$payload, $$props) {
  push();
  var $$store_subs;
  let isAddModalOpen = false;
  function toggleAddForm() {
    isAddModalOpen = true;
  }
  function handleAddCancel() {
    isAddModalOpen = false;
  }
  let loadingFocusToggle = /* @__PURE__ */ new Set();
  let {
    todos = [],
    listTitle = "",
    addButtonId = "add-todo-button"
  } = $$props;
  function triggerAddForm() {
    toggleAddForm();
  }
  $$payload.out += `<div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm py-2">`;
  TodoAddModal($$payload, {
    isOpen: isAddModalOpen,
    onCloseRequest: handleAddCancel
  });
  $$payload.out += `<!----> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> `;
  if (store_get($$store_subs ??= {}, "$todoStore", todoStore).isLoading && todos.length === 0 && store_get($$store_subs ??= {}, "$todoStore", todoStore).todos.length === 0) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="p-3 rounded-md bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 border border-blue-200 dark:border-blue-800 mx-2 my-2 text-center text-sm"><p>Loading tasks...</p></div>`;
  } else if (store_get($$store_subs ??= {}, "$todoStore", todoStore).error && todos.length === 0 && store_get($$store_subs ??= {}, "$todoStore", todoStore).todos.length === 0) {
    $$payload.out += "<!--[1-->";
    $$payload.out += `<div class="p-3 rounded-md bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 border border-red-200 dark:border-red-800 mx-2 my-2 text-center text-sm"><p class="mb-1">Failed to load tasks: ${escape_html(store_get($$store_subs ??= {}, "$todoStore", todoStore).error)}</p> <button class="px-3 py-1 text-xs font-medium text-white bg-primary-500 hover:bg-primary-600 rounded-md transition-colors">Retry</button></div>`;
  } else if (todos.length === 0) {
    $$payload.out += "<!--[2-->";
    $$payload.out += `<div class="p-4 rounded-md bg-gray-50 dark:bg-gray-700/30 text-gray-600 dark:text-gray-400 border border-dashed border-gray-300 dark:border-gray-600 mx-2 my-2 text-center text-sm"><p>No tasks in this list.</p> `;
    if (listTitle.toLowerCase().includes("active")) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<p class="text-xs mt-1">All tasks are completed or set as current focus!</p>`;
    } else {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]--></div>`;
  } else {
    $$payload.out += "<!--[!-->";
    const each_array = ensure_array_like(todos);
    $$payload.out += `<ul class="space-y-2 px-2"><!--[-->`;
    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
      let todo = each_array[$$index];
      $$payload.out += `<li${attr_class(`border border-blue-200 dark:border-blue-700 rounded-md p-3 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors ${stringify(todo.is_current_focus ? "bg-amber-50 dark:bg-amber-900/20 border-amber-200 dark:border-amber-700" : "")}`)}><div class="flex items-center"><input type="checkbox" class="w-4 h-4 mr-3 rounded border-blue-300 text-blue-500 focus:ring-blue-500"${attr("checked", todo.status === "completed", true)}> <div class="flex-grow"><div class="flex items-center"><span${attr_class(`font-medium text-blue-900 dark:text-blue-100 ${stringify(todo.status === "completed" ? "line-through text-blue-500/70 dark:text-blue-400/70" : "")}`)}>${escape_html(todo.title)}</span> `;
      if (todo.priority === "high") {
        $$payload.out += "<!--[-->";
        $$payload.out += `<span class="ml-2 text-xs px-1.5 py-0.5 bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300 rounded-full">High</span>`;
      } else if (todo.priority === "medium") {
        $$payload.out += "<!--[1-->";
        $$payload.out += `<span class="ml-2 text-xs px-1.5 py-0.5 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300 rounded-full">Medium</span>`;
      } else {
        $$payload.out += "<!--[!-->";
      }
      $$payload.out += `<!--]--></div> `;
      if (todo.due_date) {
        $$payload.out += "<!--[-->";
        $$payload.out += `<p class="text-xs text-blue-600 dark:text-blue-400 mt-1">Due: ${escape_html(new Date(todo.due_date).toLocaleDateString())}</p>`;
      } else {
        $$payload.out += "<!--[!-->";
      }
      $$payload.out += `<!--]--></div> <div class="flex space-x-1"><button${attr_class(`p-1 ${stringify(todo.is_current_focus ? "text-yellow-500 hover:text-yellow-600" : "text-gray-400 hover:text-yellow-500")} transition-colors ${stringify(loadingFocusToggle.has(todo.id) ? "opacity-50 cursor-not-allowed" : "")}`)}${attr("title", todo.is_current_focus ? "Remove from Main Focus" : "Set as Main Focus")}${attr("aria-label", todo.is_current_focus ? "Remove from Main Focus" : "Set as Main Focus")}${attr("disabled", loadingFocusToggle.has(todo.id), true)}>`;
      if (loadingFocusToggle.has(todo.id)) {
        $$payload.out += "<!--[-->";
        $$payload.out += `<div class="h-4 w-4 border-2 border-current border-t-transparent rounded-full animate-spin"></div>`;
      } else {
        $$payload.out += "<!--[!-->";
        $$payload.out += `<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>`;
      }
      $$payload.out += `<!--]--></button> <button class="p-1 text-gray-400 hover:text-blue-500 transition-colors" title="Edit" aria-label="Edit task"><svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor"><path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"></path></svg></button> <button class="p-1 text-gray-400 hover:text-red-500 transition-colors" title="Delete" aria-label="Delete task"><svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg></button></div></div></li>`;
    }
    $$payload.out += `<!--]--></ul>`;
  }
  $$payload.out += `<!--]--></div>`;
  if ($$store_subs) unsubscribe_stores($$store_subs);
  bind_props($$props, { triggerAddForm });
  pop();
}
function CurrentFocusDisplay($$payload, $$props) {
  push();
  var $$store_subs;
  let loadingFocusToggle = /* @__PURE__ */ new Set();
  let localFocusTodos = store_get($$store_subs ??= {}, "$currentFocusTodos", currentFocusTodos);
  $$payload.out += `<div class="focus-display-container">`;
  if (store_get($$store_subs ??= {}, "$todoStore", todoStore).isLoading && store_get($$store_subs ??= {}, "$todoStore", todoStore).todos.length === 0) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="p-4 bg-amber-50 dark:bg-amber-900/20 text-amber-700 dark:text-amber-300 border border-amber-200 dark:border-amber-800 rounded-md text-center"><p>Loading focus items...</p></div>`;
  } else if (localFocusTodos.length === 0) {
    $$payload.out += "<!--[1-->";
    $$payload.out += `<div class="p-6 bg-amber-50/50 dark:bg-amber-900/10 text-amber-700 dark:text-amber-400 border border-dashed border-amber-300 dark:border-amber-700 rounded-md text-center"><p>No current focus set. <br> You can mark a task as "Current Focus" from the todo list!</p></div>`;
  } else {
    $$payload.out += "<!--[!-->";
    const each_array = ensure_array_like(localFocusTodos);
    $$payload.out += `<div class="focus-item-container border border-amber-200 dark:border-amber-700 rounded-md overflow-hidden bg-amber-50/50 dark:bg-amber-900/10"><!--[-->`;
    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
      let focusItem = each_array[$$index];
      $$payload.out += `<div class="focus-item p-4 border-b border-amber-200 dark:border-amber-700 last:border-b-0"><div class="flex items-start"><div class="flex-shrink-0 mr-3"><input type="checkbox"${attr("id", `focus-status-${stringify(focusItem.id)}`)} class="w-5 h-5 rounded border-amber-300 text-amber-500 focus:ring-amber-500"${attr("checked", focusItem.status === "completed", true)}></div> <div class="flex-grow"><div class="flex items-center"><span class="text-yellow-500 mr-2"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg></span> <h3 class="text-lg font-medium text-amber-900 dark:text-amber-100">${escape_html(focusItem.title)}</h3></div> `;
      if (focusItem.description) {
        $$payload.out += "<!--[-->";
        $$payload.out += `<p class="mt-1 text-sm text-amber-700 dark:text-amber-300">${escape_html(focusItem.description)}</p>`;
      } else {
        $$payload.out += "<!--[!-->";
      }
      $$payload.out += `<!--]--> <div class="mt-2 flex flex-wrap gap-2">`;
      if (focusItem.due_date) {
        $$payload.out += "<!--[-->";
        $$payload.out += `<span class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-amber-100 dark:bg-amber-800/40 text-amber-800 dark:text-amber-200 border border-amber-200 dark:border-amber-700">Due: ${escape_html(new Date(focusItem.due_date).toLocaleDateString())}</span>`;
      } else {
        $$payload.out += "<!--[!-->";
      }
      $$payload.out += `<!--]--> <span${attr_class(`inline-flex items-center px-2 py-1 rounded-md text-xs font-medium ${stringify(focusItem.priority === "high" ? "bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300 border border-red-200 dark:border-red-800" : focusItem.priority === "medium" ? "bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300 border border-yellow-200 dark:border-yellow-800" : "bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 border border-green-200 dark:border-green-800")}`)}>${escape_html(focusItem.priority.charAt(0).toUpperCase() + focusItem.priority.slice(1))} Priority</span></div></div> <div class="flex-shrink-0 flex space-x-2 ml-4"><button class="p-1 text-amber-600 hover:text-amber-800 dark:text-amber-400 dark:hover:text-amber-300 transition-colors" title="Edit" aria-label="Edit task"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"></path></svg></button> <button class="p-1 text-amber-600 hover:text-blue-500 dark:text-amber-400 dark:hover:text-blue-400 transition-colors disabled:opacity-50" title="Remove from Main Focus" aria-label="Remove from Main Focus"${attr("disabled", loadingFocusToggle.has(focusItem.id), true)}>`;
      if (loadingFocusToggle.has(focusItem.id)) {
        $$payload.out += "<!--[-->";
        $$payload.out += `<div class="h-5 w-5 border-2 border-current border-t-transparent rounded-full animate-spin"></div>`;
      } else {
        $$payload.out += "<!--[!-->";
        $$payload.out += `<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>`;
      }
      $$payload.out += `<!--]--></button> <button class="p-1 text-amber-600 hover:text-red-500 dark:text-amber-400 dark:hover:text-red-400 transition-colors" title="Delete" aria-label="Delete task"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg></button></div></div></div>`;
    }
    $$payload.out += `<!--]--></div>`;
  }
  $$payload.out += `<!--]--> `;
  if (store_get($$store_subs ??= {}, "$todoStore", todoStore).error && store_get($$store_subs ??= {}, "$todoStore", todoStore).todos.length === 0 && !store_get($$store_subs ??= {}, "$todoStore", todoStore).isLoading) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="p-4 bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 border border-red-200 dark:border-red-800 rounded-md text-center"><p>Failed to load items: ${escape_html(store_get($$store_subs ??= {}, "$todoStore", todoStore).error)}</p></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div>`;
  if ($$store_subs) unsubscribe_stores($$store_subs);
  pop();
}
function _page($$payload, $$props) {
  push();
  var $$store_subs;
  const pageStyle = colorSchemes.doing;
  let activeTodos = store_get($$store_subs ??= {}, "$otherActiveTodos", otherActiveTodos);
  store_get($$store_subs ??= {}, "$completedTodos", completedTodos);
  onDestroy(() => {
  });
  $$payload.out += `<div${attr_class(clsx(combineClasses(pageContainer, "h-[calc(100vh-180px)] flex flex-col")))}><div class="flex-grow overflow-hidden"><div${attr_class(clsx(combineClasses(layouts.twoColumnOneThree, "h-full")))}><div${attr_class(clsx(combineClasses(columnSpans.oneFourth, "h-full flex flex-col")))}><div${attr_class(clsx(combineClasses(cardBase, pageStyle.border, "h-full flex flex-col")))}><div class="p-4 border-b border-gray-200 dark:border-gray-700"><div class="flex justify-between items-center"><h2${attr_class(clsx(combineClasses(headings.h2, pageStyle.text)))}><svg xmlns="http://www.w3.org/2000/svg"${attr_class(clsx(combineClasses("h-5 w-5 mr-2 inline", pageStyle.icon)))} viewBox="0 0 20 20" fill="currentColor"><path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"></path><path fill-rule="evenodd" d="M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v11a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z" clip-rule="evenodd"></path></svg> Todo</h2> <button id="add-todo-button"${attr_class(clsx(combineClasses("p-1 text-sm rounded-md focus:outline-none focus:ring-2", pageStyle.text, pageStyle.hover)))} aria-label="Add new todo"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"></path></svg></button></div></div> <div${attr_class(clsx(combineClasses(scrollArea.container, "flex-grow relative")))}><div${attr_class(clsx(combineClasses(scrollArea.indicator, "left-0", pageStyle.scrollbar)))}></div> <div${attr_class(clsx(combineClasses("pl-3", "absolute inset-0 overflow-y-auto pr-2")))}>`;
  {
    $$payload.out += "<!--[-->";
    TodoListSidebar($$payload, {
      todos: activeTodos,
      addButtonId: "add-todo-button"
    });
  }
  $$payload.out += `<!--]--></div></div> <div${attr_class(clsx(combineClasses("p-4 border-t", pageStyle.border)))}><button${attr_class(clsx(combineClasses("flex items-center justify-between w-full text-left p-2 rounded-md transition-colors", pageStyle.hover)))}><h3${attr_class(clsx(combineClasses("text-lg font-medium", pageStyle.text)))}>${escape_html("PAST")}</h3> <span${attr_class(clsx(combineClasses("text-sm", pageStyle.text)))}>${escape_html("Show completed tasks")}</span></button></div></div></div> <div${attr_class(clsx(combineClasses(columnSpans.threeFourths, "h-full flex flex-col")))}><div${attr_class(clsx(combineClasses(cardBase, pageStyle.border, "h-full flex flex-col")))}><div class="p-6 border-b border-gray-200 dark:border-gray-700"><h1${attr_class(clsx(combineClasses(headings.h1, pageStyle.text)))}><span${attr_class(clsx(combineClasses(pageStyle.icon, "mr-2")))}>⭐</span> Main Focus:</h1></div> <div class="p-6 flex-grow overflow-auto">`;
  CurrentFocusDisplay($$payload);
  $$payload.out += `<!----></div></div></div></div></div></div>`;
  if ($$store_subs) unsubscribe_stores($$store_subs);
  pop();
}
export {
  _page as default
};
