import { R as attr, I as escape_html, J as stringify, G as attr_class, D as pop, A as push, E as store_get, F as ensure_array_like, K as unsubscribe_stores, T as clsx } from "../../../../chunks/index.js";
import { g as get, w as writable } from "../../../../chunks/index2.js";
import { a as api } from "../../../../chunks/authService.js";
import { a as authStore } from "../../../../chunks/authStore.js";
import { c as combineClasses, p as pageContainer, l as layouts, a as columnSpans, b as colorSchemes, d as cardBase, h as headings, s as scrollArea } from "../../../../chunks/pageStyles.js";
const BASE_URL = "/plans/";
const futurePlanService = {
  /**
   * Retrieves all future plans for the authenticated user.
   * GET /api/plans
   * Ordered by target_date asc (nulls last), then created_at desc.
   * @returns Promise<FuturePlan[]> - A list of future plans.
   */
  async getFuturePlans() {
    try {
      const responseData = await api.get(BASE_URL);
      return responseData;
    } catch (error) {
      console.error("Error fetching future plans:", error);
      throw error;
    }
  },
  /**
   * Creates a new future plan.
   * POST /api/plans
   * @param planData - The data for the new future plan.
   * @returns Promise<FuturePlan> - The newly created future plan.
   */
  async createFuturePlan(planData) {
    try {
      const responseData = await api.post(BASE_URL, planData);
      return responseData;
    } catch (error) {
      console.error("Error creating future plan:", error);
      throw error;
    }
  },
  /**
   * Retrieves a specific future plan by its ID.
   * GET /api/plans/<plan_id>
   * @param planId - The ID of the future plan to retrieve.
   * @returns Promise<FuturePlan> - The requested future plan.
   */
  async getFuturePlanById(planId) {
    try {
      const responseData = await api.get(`${BASE_URL}/${planId}`);
      return responseData;
    } catch (error) {
      console.error(`Error fetching future plan with ID ${planId}:`, error);
      throw error;
    }
  },
  /**
   * Updates an existing future plan.
   * PUT /api/plans/<plan_id>
   * @param planId - The ID of the future plan to update.
   * @param planData - The data to update the future plan with.
   * @returns Promise<FuturePlan> - The updated future plan.
   */
  async updateFuturePlan(planId, planData) {
    try {
      const responseData = await api.put(`${BASE_URL}${planId}`, planData);
      return responseData;
    } catch (error) {
      console.error(`Error updating future plan with ID ${planId}:`, error);
      throw error;
    }
  },
  /**
   * Deletes a specific future plan.
   * DELETE /api/plans/<plan_id>
   * @param planId - The ID of the future plan to delete.
   * @returns Promise<void> - Resolves when deletion is successful.
   */
  async deleteFuturePlan(planId) {
    try {
      await api.delete(`${BASE_URL}${planId}`);
    } catch (error) {
      console.error(`Error deleting future plan with ID ${planId}:`, error);
      throw error;
    }
  }
};
const createFuturePlanStore = () => {
  const futurePlans = writable([]);
  const isLoading = writable(false);
  const error = writable(null);
  let currentUserId = null;
  authStore.subscribe((auth) => {
    currentUserId = auth.user ? auth.user.id : null;
  });
  const loadFuturePlans = async () => {
    isLoading.set(true);
    error.set(null);
    try {
      if (currentUserId) {
        try {
          const fetchedPlans = await futurePlanService.getFuturePlans();
          if (fetchedPlans && fetchedPlans.length > 0) {
            futurePlans.set(fetchedPlans);
          }
        } catch (apiError) {
          console.warn("API请求失败，使用模拟数据", apiError);
        }
      }
    } catch (e) {
      error.set(e.message || "加载计划失败");
    } finally {
      isLoading.set(false);
    }
  };
  const addFuturePlan = async (planData) => {
    if (!currentUserId) {
      error.set("User not authenticated. Cannot add future plan.");
      return null;
    }
    isLoading.set(true);
    error.set(null);
    try {
      const newPlan = await futurePlanService.createFuturePlan(planData);
      if (newPlan) {
        futurePlans.update((items) => [newPlan, ...items].sort((a, b) => {
          const dateA = a.created_at ? new Date(a.created_at).getTime() : 0;
          const dateB = b.created_at ? new Date(b.created_at).getTime() : 0;
          return dateB - dateA;
        }));
      }
      return newPlan;
    } catch (e) {
      error.set(e.message || "Failed to add future plan.");
      return null;
    } finally {
      isLoading.set(false);
    }
  };
  const updateFuturePlan = async (planId, planData) => {
    isLoading.set(true);
    error.set(null);
    try {
      const updatedPlan = await futurePlanService.updateFuturePlan(planId, planData);
      if (updatedPlan) {
        futurePlans.update(
          (items) => items.map((item) => item.id === planId ? updatedPlan : item).sort((a, b) => {
            const dateA = a.created_at ? new Date(a.created_at).getTime() : 0;
            const dateB = b.created_at ? new Date(b.created_at).getTime() : 0;
            return dateB - dateA;
          })
        );
      }
      return updatedPlan;
    } catch (e) {
      error.set(e.message || "Failed to update future plan.");
      return null;
    } finally {
      isLoading.set(false);
    }
  };
  const deleteFuturePlan = async (planId) => {
    isLoading.set(true);
    error.set(null);
    try {
      await futurePlanService.deleteFuturePlan(planId);
      futurePlans.update((items) => items.filter((item) => item.id !== planId));
      return true;
    } catch (e) {
      error.set(e.message || "Failed to delete future plan.");
      return false;
    } finally {
      isLoading.set(false);
    }
  };
  const getFuturePlanById = (planId) => {
    return get(futurePlans).find((item) => item.id === planId);
  };
  return {
    futurePlans,
    isLoading,
    error,
    loadFuturePlans,
    addFuturePlan,
    updateFuturePlan,
    deleteFuturePlan,
    getFuturePlanById
  };
};
const futurePlanStore = createFuturePlanStore();
authStore.subscribe((auth) => {
  if (auth.user) {
    const isLoadingVal = get(futurePlanStore.isLoading);
    const plansVal = get(futurePlanStore.futurePlans);
    if (!isLoadingVal && plansVal.length === 0) {
      futurePlanStore.loadFuturePlans();
    }
  } else {
    futurePlanStore.futurePlans.set([]);
  }
});
function FuturePlanItem($$payload, $$props) {
  push();
  let {
    futurePlan
  } = $$props;
  let isDeleting = false;
  function formatDate(dateString) {
    if (!dateString) return "Not set";
    try {
      const date = /* @__PURE__ */ new Date(dateString + "T00:00:00");
      return new Intl.DateTimeFormat("en-US", {
        // Using 'en-US' for a common format, adjust as needed
        year: "numeric",
        month: "long",
        day: "numeric"
      }).format(date);
    } catch (e) {
      return dateString;
    }
  }
  function formatStatus(status) {
    if (!status) return "N/A";
    return status.charAt(0).toUpperCase() + status.slice(1);
  }
  const statusClasses = {
    active: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
    achieved: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
    deferred: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",
    abandoned: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300"
  };
  $$payload.out += `<div class="bg-white dark:bg-gray-800 shadow-md rounded-lg border border-green-200 dark:border-green-800 overflow-hidden cursor-pointer hover:shadow-lg transition-shadow mb-3" role="button" tabindex="0"${attr("aria-label", `View details for plan: ${stringify(futurePlan.title)}`)}><div class="border-b border-green-100 dark:border-green-800 p-4"><div class="flex justify-between items-center"><h4 class="text-lg font-semibold text-green-700 dark:text-green-300">${escape_html(futurePlan.title)}</h4> <div class="flex space-x-1"><button${attr("aria-label", `Edit plan: ${stringify(futurePlan.title)}`)} class="p-1.5 text-sm font-medium text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300 focus:outline-none focus:ring-2 focus:ring-green-500 rounded-md"><svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor"><path d="M17.414 2.586a2 2 0 00-2.828 0L7 10.172V13h2.828l7.586-7.586a2 2 0 000-2.828z"></path><path fill-rule="evenodd" d="M2 6a2 2 0 012-2h4a1 1 0 010 2H4v10h10v-4a1 1 0 112 0v4a2 2 0 01-2 2H4a2 2 0 01-2-2V6z" clip-rule="evenodd"></path></svg></button> <button${attr("disabled", isDeleting, true)}${attr("aria-label", `Delete plan: ${stringify(futurePlan.title)}`)} class="p-1.5 text-sm font-medium text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 focus:outline-none focus:ring-2 focus:ring-red-500 rounded-md disabled:opacity-50">`;
  {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>`;
  }
  $$payload.out += `<!--]--></button></div></div> `;
  if (futurePlan.target_date) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Target date: ${escape_html(formatDate(futurePlan.target_date))}</p>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div class="p-4"><div class="mb-3"><h5 class="text-sm font-semibold text-gray-600 dark:text-gray-400 mb-1">Status:</h5> <span${attr_class(`px-2 py-0.5 text-xs font-medium rounded-full ${stringify(statusClasses[futurePlan.status] || "bg-gray-100 text-gray-800 dark:bg-gray-800/80 dark:text-gray-300")}`)}>${escape_html(formatStatus(futurePlan.status))}</span></div> `;
  if (futurePlan.goal_type) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="mb-3"><h5 class="text-sm font-semibold text-gray-600 dark:text-gray-400 mb-1">Goal Type:</h5> <span class="px-2 py-0.5 text-xs font-medium text-green-700 bg-green-100 rounded-full dark:bg-green-900 dark:text-green-300">${escape_html(futurePlan.goal_type)}</span></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div class="px-4 py-2 bg-green-50 dark:bg-green-900/20 text-xs text-gray-500 dark:text-gray-400 border-t border-green-100 dark:border-green-800"><div class="flex justify-between"><span>ID: ${escape_html(futurePlan.id)}</span> <span>${escape_html(formatDate(futurePlan.created_at))} `;
  if (futurePlan.updated_at && futurePlan.updated_at !== futurePlan.created_at) {
    $$payload.out += "<!--[-->";
    $$payload.out += `(Updated: ${escape_html(formatDate(futurePlan.updated_at))})`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></span></div></div></div>`;
  pop();
}
function FuturePlanList($$payload, $$props) {
  push();
  var $$store_subs;
  const futurePlans = futurePlanStore.futurePlans;
  const isLoading = futurePlanStore.isLoading;
  const error = futurePlanStore.error;
  $$payload.out += `<div class="py-2">`;
  if (store_get($$store_subs ??= {}, "$isLoading", isLoading) && store_get($$store_subs ??= {}, "$futurePlans", futurePlans).length === 0) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="text-center py-6"><div role="status" class="flex justify-center items-center"><svg aria-hidden="true" class="w-8 h-8 text-green-200 animate-spin dark:text-green-700 fill-green-600" viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z" fill="currentColor"></path><path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0492C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z" fill="lightgray"></path></svg> <span class="sr-only">Loading...</span></div> <p class="mt-2 text-sm text-green-600 dark:text-green-400">Loading plans...</p></div>`;
  } else if (store_get($$store_subs ??= {}, "$error", error)) {
    $$payload.out += "<!--[1-->";
    $$payload.out += `<div class="p-3 mx-2 mb-3 text-xs text-red-700 bg-red-100 rounded-lg dark:bg-red-200 dark:text-red-800 text-center" role="alert"><span class="font-medium">Error:</span> ${escape_html(store_get($$store_subs ??= {}, "$error", error))} <button class="ml-2 px-2 py-1 text-xs font-medium text-white bg-green-600 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500">Retry</button></div>`;
  } else if (store_get($$store_subs ??= {}, "$futurePlans", futurePlans).length === 0) {
    $$payload.out += "<!--[2-->";
    $$payload.out += `<div class="text-center py-6"><svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-10 w-10 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1"><path stroke-linecap="round" stroke-linejoin="round" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path></svg> <h3 class="mt-2 text-sm font-medium text-green-900 dark:text-green-100">No plans yet</h3> <p class="mt-1 text-xs text-green-600 dark:text-green-400">Click + to add one</p></div>`;
  } else {
    $$payload.out += "<!--[!-->";
    const each_array = ensure_array_like(store_get($$store_subs ??= {}, "$futurePlans", futurePlans));
    $$payload.out += `<div class="space-y-2 px-2"><!--[-->`;
    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
      let plan = each_array[$$index];
      FuturePlanItem($$payload, {
        futurePlan: plan
      });
    }
    $$payload.out += `<!--]--></div>`;
  }
  $$payload.out += `<!--]--></div>`;
  if ($$store_subs) unsubscribe_stores($$store_subs);
  pop();
}
function _page($$payload, $$props) {
  push();
  const pageStyle = colorSchemes.plan;
  $$payload.out += `<div${attr_class(clsx(combineClasses(pageContainer, "h-[calc(100vh-180px)] flex flex-col")))}>`;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> <div class="flex-grow overflow-hidden"><div${attr_class(clsx(combineClasses(layouts.twoColumnOneThree, "h-full")))}><div${attr_class(clsx(combineClasses(columnSpans.oneFourth, "h-full flex flex-col")))}><div${attr_class(clsx(combineClasses(cardBase, pageStyle.border, "h-full flex flex-col")))}><div class="p-4 border-b border-gray-200 dark:border-gray-700"><div class="flex justify-between items-center"><h2${attr_class(clsx(combineClasses(headings.h3, pageStyle.text)))}><svg xmlns="http://www.w3.org/2000/svg"${attr_class(clsx(combineClasses("h-5 w-5 mr-2 inline", pageStyle.icon)))} viewBox="0 0 448 512" fill="currentColor"><path d="M64 80c-8.8 0-16 7.2-16 16l0 320c0 8.8 7.2 16 16 16l224 0 0-80c0-17.7 14.3-32 32-32l80 0 0-224c0-8.8-7.2-16-16-16L64 80zM288 480L64 480c-35.3 0-64-28.7-64-64L0 96C0 60.7 28.7 32 64 32l320 0c35.3 0 64 28.7 64 64l0 224 0 5.5c0 17-6.7 33.3-18.7 45.3l-90.5 90.5c-12 12-28.3 18.7-45.3 18.7l-5.5 0z"></path></svg> Plans</h2> <button${attr_class(clsx(combineClasses("p-1 text-sm rounded-md focus:outline-none focus:ring-2", pageStyle.text, pageStyle.hover)))} aria-label="Add new plan"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"></path></svg></button></div></div> <div${attr_class(clsx(combineClasses(scrollArea.container, "flex-grow relative")))}><div${attr_class(clsx(combineClasses(scrollArea.indicator, "left-0", pageStyle.scrollbar)))}></div> <div${attr_class(clsx(combineClasses("pl-3", "absolute inset-0 overflow-y-auto pr-2")))}>`;
  FuturePlanList($$payload);
  $$payload.out += `<!----></div></div></div></div> <div${attr_class(clsx(combineClasses(columnSpans.threeFourths, "h-full flex flex-col")))}><div${attr_class(clsx(combineClasses(cardBase, pageStyle.border, "h-full flex flex-col")))}><div class="flex-grow overflow-hidden"><div class="h-full overflow-y-auto p-6">`;
  {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<div class="flex flex-col items-center justify-center h-full"><svg xmlns="http://www.w3.org/2000/svg"${attr_class(clsx(combineClasses("h-16 w-16 mb-4", pageStyle.icon)))} viewBox="0 0 448 512" fill="currentColor"><path d="M64 80c-8.8 0-16 7.2-16 16l0 320c0 8.8 7.2 16 16 16l224 0 0-80c0-17.7 14.3-32 32-32l80 0 0-224c0-8.8-7.2-16-16-16L64 80zM288 480L64 480c-35.3 0-64-28.7-64-64L0 96C0 60.7 28.7 32 64 32l320 0c35.3 0 64 28.7 64 64l0 224 0 5.5c0 17-6.7 33.3-18.7 45.3l-90.5 90.5c-12 12-28.3 18.7-45.3 18.7l-5.5 0z"></path></svg> <h3${attr_class(clsx(combineClasses("text-xl font-medium mb-2", pageStyle.text)))}>Select a Plan</h3> <p class="text-gray-600 dark:text-gray-400 text-center max-w-md">Select a plan from the list to view details, or click the "+" button to create a new plan.</p></div>`;
  }
  $$payload.out += `<!--]--></div></div></div></div></div></div> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div>`;
  pop();
}
export {
  _page as default
};
