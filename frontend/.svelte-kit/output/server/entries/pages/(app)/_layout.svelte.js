import "clsx";
import { N as spread_attributes, O as attr_style, D as pop, A as push } from "../../../chunks/index.js";
/* empty css                  */
import "../../../chunks/client.js";
import { p as page } from "../../../chunks/index3.js";
import "../../../chunks/authStore.js";
import "../../../chunks/authService.js";
import "../../../chunks/themeStore.js";
function PageLoadingIndicator($$payload, $$props) {
  push();
  let {
    height = "3px",
    color = "var(--color-primary-500, #6366f1)",
    class: className = "",
    $$slots,
    $$events,
    ...rest
  } = $$props;
  let progress = 0;
  $$payload.out += `<div${spread_attributes(
    {
      class: `fixed top-0 left-0 right-0 z-50 ${"pointer-events-none"} ${className} ${"opacity-0"}`,
      style: "transition: opacity 300ms ease-in-out;",
      ...rest
    }
  )}><div class="relative h-full"><div class="absolute top-0 left-0 right-0 bg-gray-200 dark:bg-gray-700 opacity-30"${attr_style(`height: ${height};`)}></div> <div class="h-full bg-current relative"${attr_style(`
        height: ${height};
        width: ${progress}%;
        transition: width 300ms ease-out, opacity 300ms ease-in-out;
        background-color: ${color};
        ${""}
      `)}><div class="absolute right-0 top-0 bottom-0 w-4 bg-white opacity-50 animate-pulse"${attr_style(`height: ${height};`)}></div></div></div> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div>`;
  pop();
}
function _layout($$payload, $$props) {
  push();
  page.url.pathname.startsWith("/anchor");
  page.url.pathname;
  PageLoadingIndicator($$payload, {});
  $$payload.out += `<!----> `;
  {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<div class="flex justify-center items-center min-h-screen text-2xl text-secondary-500 dark:text-secondary-400"><div class="flex flex-col items-center"><div class="w-16 h-16 border-4 border-primary-500 border-t-transparent rounded-full animate-spin mb-4"></div> <p>Loading...</p></div></div>`;
  }
  $$payload.out += `<!--]-->`;
  pop();
}
export {
  _layout as default
};
