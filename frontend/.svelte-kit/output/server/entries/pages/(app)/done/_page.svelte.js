import { R as attr, I as escape_html, J as stringify, F as ensure_array_like, D as pop, A as push, E as store_get, K as unsubscribe_stores, G as attr_class, T as clsx } from "../../../../chunks/index.js";
import { g as get, w as writable } from "../../../../chunks/index2.js";
import { a as api } from "../../../../chunks/authService.js";
import { a as authStore } from "../../../../chunks/authStore.js";
import { c as combineClasses, p as pageContainer, l as layouts, a as columnSpans, b as colorSchemes, d as cardBase, h as headings, s as scrollArea } from "../../../../chunks/pageStyles.js";
const BASE_URL = "/achievements/";
const achievementService = {
  /**
   * Retrieves all achievements for the authenticated user.
   * GET /api/achievements
   * @returns Promise<Achievement[]>
   */
  async getAchievements() {
    try {
      const responseData = await api.get(BASE_URL);
      return responseData;
    } catch (error) {
      console.error("Error fetching achievements:", error);
      throw error;
    }
  },
  /**
   * Creates a new achievement.
   * POST /api/achievements
   * @param achievementData - The data for the new achievement.
   * @returns Promise<Achievement> - The newly created achievement.
   */
  async createAchievement(achievementData) {
    try {
      const responseData = await api.post(BASE_URL, achievementData);
      return responseData;
    } catch (error) {
      console.error("Error creating achievement:", error);
      throw error;
    }
  },
  /**
   * Retrieves a specific achievement by its ID.
   * GET /api/achievements/<achievement_id>
   * @param id - The ID of the achievement to retrieve.
   * @returns Promise<Achievement> - The requested achievement.
   */
  async getAchievementById(id) {
    try {
      const responseData = await api.get(`${BASE_URL}/${id}`);
      return responseData;
    } catch (error) {
      console.error(`Error fetching achievement with ID ${id}:`, error);
      throw error;
    }
  },
  /**
   * Updates an existing achievement.
   * PUT /api/achievements/<achievement_id>
   * @param id - The ID of the achievement to update.
   * @param achievementData - The data to update the achievement with.
   * @returns Promise<Achievement> - The updated achievement.
   */
  async updateAchievement(id, achievementData) {
    try {
      const responseData = await api.put(`${BASE_URL}${id}`, achievementData);
      return responseData;
    } catch (error) {
      console.error(`Error updating achievement with ID ${id}:`, error);
      throw error;
    }
  },
  /**
   * Deletes a specific achievement.
   * DELETE /api/achievements/<achievement_id>
   * @param id - The ID of the achievement to delete.
   * @returns Promise<void>
   */
  async deleteAchievement(id) {
    try {
      await api.delete(`${BASE_URL}${id}`);
    } catch (error) {
      console.error(`Error deleting achievement with ID ${id}:`, error);
      throw error;
    }
  }
};
const createAchievementStore = () => {
  const achievements = writable([]);
  const isLoading = writable(false);
  const error = writable(null);
  let currentUserId = null;
  authStore.subscribe((auth) => {
    currentUserId = auth.user ? auth.user.id : null;
  });
  const loadAchievements = async () => {
    if (!currentUserId) {
      error.set("用户未登录，无法加载成就。");
      achievements.set([]);
      return;
    }
    isLoading.set(true);
    error.set(null);
    try {
      const fetchedAchievements = await achievementService.getAchievements();
      achievements.set(fetchedAchievements || []);
    } catch (e) {
      error.set(e.message || "加载成就失败。");
      achievements.set([]);
    } finally {
      isLoading.set(false);
    }
  };
  const addAchievement = async (achievementData) => {
    if (!currentUserId) {
      error.set("用户未登录，无法添加成就。");
      return null;
    }
    isLoading.set(true);
    error.set(null);
    try {
      const newAchievement = await achievementService.createAchievement(achievementData);
      if (newAchievement) {
        achievements.update(
          (items) => [newAchievement, ...items].sort((a, b) => {
            if (!a.date_achieved || !b.date_achieved) return 0;
            return new Date(b.date_achieved).getTime() - new Date(a.date_achieved).getTime();
          })
        );
      }
      return newAchievement;
    } catch (e) {
      error.set(e.message || "添加成就失败。");
      return null;
    } finally {
      isLoading.set(false);
    }
  };
  const updateAchievement = async (idInput, achievementData) => {
    const id = typeof idInput === "string" ? parseInt(idInput, 10) : idInput;
    if (isNaN(id)) {
      error.set("无效的成就ID进行更新。");
      return null;
    }
    isLoading.set(true);
    error.set(null);
    try {
      const updatedAchievement = await achievementService.updateAchievement(id, achievementData);
      if (updatedAchievement) {
        achievements.update(
          (items) => items.map((item) => item.id === id ? updatedAchievement : item).sort((a, b) => {
            if (!a.date_achieved || !b.date_achieved) return 0;
            return new Date(b.date_achieved).getTime() - new Date(a.date_achieved).getTime();
          })
        );
      }
      return updatedAchievement;
    } catch (e) {
      error.set(e.message || "更新成就失败。");
      return null;
    } finally {
      isLoading.set(false);
    }
  };
  const deleteAchievement = async (idInput) => {
    const id = typeof idInput === "string" ? parseInt(idInput, 10) : idInput;
    if (isNaN(id)) {
      error.set("无效的成就ID进行删除。");
      return false;
    }
    isLoading.set(true);
    error.set(null);
    try {
      await achievementService.deleteAchievement(id);
      achievements.update((items) => items.filter((item) => item.id !== id));
      return true;
    } catch (e) {
      error.set(e.message || "删除成就失败。");
      return false;
    } finally {
      isLoading.set(false);
    }
  };
  const getAchievementById = (idInput) => {
    const id = typeof idInput === "string" ? parseInt(idInput, 10) : idInput;
    if (isNaN(id)) return void 0;
    return get(achievements).find((item) => item.id === id);
  };
  return {
    achievements,
    // Expose the Writable store for achievements
    isLoading,
    // Expose the Writable store for loading state
    error,
    // Expose the Writable store for error state
    loadAchievements,
    addAchievement,
    updateAchievement,
    deleteAchievement,
    getAchievementById
  };
};
const achievementStore = createAchievementStore();
authStore.subscribe((auth) => {
  if (auth.user) {
    const isLoadingVal = get(achievementStore.isLoading);
    const achievementsVal = get(achievementStore.achievements);
    if (!isLoadingVal && achievementsVal.length === 0) {
      achievementStore.loadAchievements();
    }
  } else {
    achievementStore.achievements.set([]);
  }
});
function AchievementItem($$payload, $$props) {
  push();
  let { achievement } = $$props;
  let isDeleting = false;
  function formatDate(dateString) {
    if (!dateString) return "日期未指定";
    try {
      const date = new Date(dateString);
      const userTimezoneOffset = date.getTimezoneOffset() * 6e4;
      return new Date(date.getTime() + userTimezoneOffset).toLocaleDateString(void 0, {
        year: "numeric",
        month: "long",
        day: "numeric"
      });
    } catch (e) {
      return dateString;
    }
  }
  $$payload.out += `<div class="bg-white dark:bg-gray-800 shadow-md rounded-lg border border-purple-200 dark:border-purple-800 overflow-hidden cursor-pointer hover:shadow-lg transition-shadow" role="button" tabindex="0"${attr("aria-label", `View details for achievement: ${stringify(achievement.title)}`)}><div class="border-b border-purple-100 dark:border-purple-800 p-4"><div class="flex justify-between items-center"><h4 class="text-lg font-semibold text-purple-700 dark:text-purple-300">${escape_html(achievement.title)}</h4> <div class="flex space-x-1"><button${attr("aria-label", `Edit achievement ${stringify(achievement.title)}`)} class="p-1.5 text-sm font-medium text-purple-600 hover:text-purple-800 dark:text-purple-400 dark:hover:text-purple-300 focus:outline-none focus:ring-2 focus:ring-purple-500 rounded-md"><svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor"><path d="M17.414 2.586a2 2 0 00-2.828 0L7 10.172V13h2.828l7.586-7.586a2 2 0 000-2.828z"></path><path fill-rule="evenodd" d="M2 6a2 2 0 012-2h4a1 1 0 010 2H4v10h10v-4a1 1 0 112 0v4a2 2 0 01-2 2H4a2 2 0 01-2-2V6z" clip-rule="evenodd"></path></svg></button> <button${attr("disabled", isDeleting, true)}${attr("aria-label", `Delete achievement ${stringify(achievement.title)}`)} class="p-1.5 text-sm font-medium text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 focus:outline-none focus:ring-2 focus:ring-red-500 rounded-md disabled:opacity-50">`;
  {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>`;
  }
  $$payload.out += `<!--]--></button></div></div> `;
  if (achievement.date_achieved) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Achieved on: ${escape_html(formatDate(achievement.date_achieved))}</p>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div class="p-4">`;
  if (achievement.core_skills_json && achievement.core_skills_json.length > 0) {
    $$payload.out += "<!--[-->";
    const each_array = ensure_array_like(achievement.core_skills_json);
    $$payload.out += `<div class="mb-3"><h5 class="text-sm font-semibold text-gray-600 dark:text-gray-400 mb-1">Core Skills:</h5> <div class="flex flex-wrap gap-1.5"><!--[-->`;
    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
      let skill = each_array[$$index];
      $$payload.out += `<span class="px-2 py-0.5 text-xs font-medium text-purple-700 bg-purple-100 rounded-full dark:bg-purple-900 dark:text-purple-300">${escape_html(skill)}</span>`;
    }
    $$payload.out += `<!--]--></div></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> `;
  if (achievement.description) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="mb-3"><h5 class="text-sm font-semibold text-gray-600 dark:text-gray-400 mb-1">Description:</h5> <p class="text-gray-700 dark:text-gray-300 text-sm">${escape_html(achievement.description)}</p></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> `;
  if (achievement.quantifiable_results) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="mb-3"><h5 class="text-sm font-semibold text-gray-600 dark:text-gray-400 mb-1">Results:</h5> <p class="text-gray-700 dark:text-gray-300 text-sm">${escape_html(achievement.quantifiable_results)}</p></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div class="px-4 py-2 bg-purple-50 dark:bg-purple-900/20 text-xs text-gray-500 dark:text-gray-400 border-t border-purple-100 dark:border-purple-800"><div class="flex justify-between"><span>ID: ${escape_html(achievement.id)}</span> <span>${escape_html(formatDate(achievement.created_at))} `;
  if (achievement.updated_at && achievement.updated_at !== achievement.created_at) {
    $$payload.out += "<!--[-->";
    $$payload.out += `(Updated: ${escape_html(formatDate(achievement.updated_at))})`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></span></div></div></div>`;
  pop();
}
function AchievementList($$payload, $$props) {
  push();
  var $$store_subs;
  const achievements = achievementStore.achievements;
  const isLoading = achievementStore.isLoading;
  const error = achievementStore.error;
  $$payload.out += `<div class="flex flex-col h-full py-2"><div class="flex-grow">`;
  if (store_get($$store_subs ??= {}, "$isLoading", isLoading) && store_get($$store_subs ??= {}, "$achievements", achievements).length === 0) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="text-center py-6"><div role="status" class="flex justify-center items-center"><svg aria-hidden="true" class="w-10 h-10 text-purple-200 animate-spin dark:text-purple-700 fill-purple-600" viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z" fill="currentColor"></path><path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0492C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z" fill="lightgray"></path></svg> <span class="sr-only">Loading...</span></div> <p class="mt-2 text-purple-600 dark:text-purple-400">Loading achievements...</p></div>`;
  } else if (store_get($$store_subs ??= {}, "$error", error)) {
    $$payload.out += "<!--[1-->";
    $$payload.out += `<div class="p-4 mx-2 mb-4 text-sm text-red-700 bg-red-100 rounded-lg dark:bg-red-200 dark:text-red-800 text-center" role="alert"><span class="font-medium">Loading error:</span> ${escape_html(store_get($$store_subs ??= {}, "$error", error))} <button class="ml-4 px-3 py-1.5 text-sm font-medium text-white bg-purple-600 rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500">Retry</button></div>`;
  } else if (store_get($$store_subs ??= {}, "$achievements", achievements).length === 0) {
    $$payload.out += "<!--[2-->";
    $$payload.out += `<div class="text-center py-6"><svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-purple-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1"><path stroke-linecap="round" stroke-linejoin="round" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16l3.5-2 3.5 2 3.5-2 3.5 2zM12 11v2m0-4h.01"></path></svg> <h3 class="mt-2 text-lg font-medium text-purple-900 dark:text-purple-100">No achievements yet</h3> <p class="mt-1 text-sm text-purple-600 dark:text-purple-400">Start adding your first achievement!</p></div>`;
  } else {
    $$payload.out += "<!--[!-->";
    const each_array = ensure_array_like(store_get($$store_subs ??= {}, "$achievements", achievements));
    $$payload.out += `<div class="space-y-4 px-2"><!--[-->`;
    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
      let achievement = each_array[$$index];
      AchievementItem($$payload, {
        achievement
      });
    }
    $$payload.out += `<!--]--></div>`;
  }
  $$payload.out += `<!--]--></div></div>`;
  if ($$store_subs) unsubscribe_stores($$store_subs);
  pop();
}
function _page($$payload, $$props) {
  push();
  const pageStyle = colorSchemes.done;
  $$payload.out += `<div${attr_class(clsx(combineClasses(pageContainer, "h-[calc(100vh-180px)] flex flex-col")))}>`;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> <div class="flex-grow overflow-hidden"><div${attr_class(clsx(combineClasses(layouts.twoColumnOneThree, "h-full")))}><div${attr_class(clsx(combineClasses(columnSpans.oneFourth, "h-full flex flex-col")))}><div${attr_class(clsx(combineClasses(cardBase, pageStyle.border, "h-full flex flex-col")))}><div class="p-4 border-b border-gray-200 dark:border-gray-700"><div class="flex justify-between items-center"><h2${attr_class(clsx(combineClasses(headings.h3, pageStyle.text)))}><svg xmlns="http://www.w3.org/2000/svg"${attr_class(clsx(combineClasses("h-5 w-5 mr-2 inline", pageStyle.icon)))} viewBox="0 0 512 512" fill="currentColor"><path d="M256 512A256 256 0 1 0 256 0a256 256 0 1 0 0 512zM369 209L241 337c-9.4 9.4-24.6 9.4-33.9 0l-64-64c-9.4-9.4-9.4-24.6 0-33.9s24.6-9.4 33.9 0l47 47L335 175c9.4-9.4 24.6-9.4 33.9 0s9.4 24.6 0 33.9z"></path></svg> Achievements</h2> <button${attr_class(clsx(combineClasses("p-1 text-sm rounded-md focus:outline-none focus:ring-2", pageStyle.text, pageStyle.hover)))} aria-label="Add new achievement"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"></path></svg></button></div></div> <div${attr_class(clsx(combineClasses(scrollArea.container, "flex-grow relative")))}><div${attr_class(clsx(combineClasses(scrollArea.indicator, "left-0", pageStyle.scrollbar)))}></div> <div${attr_class(clsx(combineClasses("pl-3", "absolute inset-0 overflow-y-auto pr-2")))}>`;
  AchievementList($$payload);
  $$payload.out += `<!----></div></div></div></div> <div${attr_class(clsx(combineClasses(columnSpans.threeFourths, "h-full flex flex-col")))}><div${attr_class(clsx(combineClasses(cardBase, pageStyle.border, "h-full flex flex-col")))}><div class="flex-grow overflow-hidden"><div class="h-full overflow-y-auto p-6">`;
  {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<div class="flex flex-col items-center justify-center h-full"><svg xmlns="http://www.w3.org/2000/svg"${attr_class(clsx(combineClasses("h-16 w-16 mb-4", pageStyle.icon)))} viewBox="0 0 512 512" fill="currentColor"><path d="M256 512A256 256 0 1 0 256 0a256 256 0 1 0 0 512zM369 209L241 337c-9.4 9.4-24.6 9.4-33.9 0l-64-64c-9.4-9.4-9.4-24.6 0-33.9s24.6-9.4 33.9 0l47 47L335 175c9.4-9.4 24.6-9.4 33.9 0s9.4 24.6 0 33.9z"></path></svg> <h3${attr_class(clsx(combineClasses("text-xl font-medium mb-2", pageStyle.text)))}>Select an Achievement</h3> <p class="text-gray-600 dark:text-gray-400 text-center max-w-md">Select an achievement from the list to view details, or click the "+" button to add a new achievement.</p></div>`;
  }
  $$payload.out += `<!--]--></div></div></div></div></div></div> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div>`;
  pop();
}
export {
  _page as default
};
