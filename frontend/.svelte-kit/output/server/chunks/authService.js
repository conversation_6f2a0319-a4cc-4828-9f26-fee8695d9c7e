import { a as authStore } from "./authStore.js";
import { g as get } from "./index2.js";
import "./client.js";
const BASE_URL = "";
{
  console.warn(
    "VITE_API_BASE_URL is not defined in your .env file. API calls may fail."
  );
}
let isRefreshing = false;
let failedQueue = [];
function processQueue(error, token = null) {
  failedQueue.forEach((prom) => {
    if (error) {
      prom.reject(error);
    } else if (token) {
      request(prom.config.endpoint, prom.config.method, prom.config.body, prom.config.requiresAuth, prom.config.customHeaders, true).then(prom.resolve).catch(prom.reject);
    }
  });
  failedQueue = [];
}
async function request(endpoint, method, body = null, requiresAuth = true, customHeaders = {}, isRetry = false) {
  const configArgs = { endpoint, method, body, requiresAuth, customHeaders, isRetry };
  const url = `${BASE_URL}${endpoint}`;
  const headers = {
    "Content-Type": "application/json",
    ...customHeaders
  };
  const currentAuth = get(authStore);
  if (requiresAuth && currentAuth && currentAuth.accessToken) {
    headers["Authorization"] = `Bearer ${currentAuth.accessToken}`;
  } else if (requiresAuth && (!currentAuth || !currentAuth.accessToken) && !isRefreshing) {
    console.warn(`Authenticated request to ${url} without an access token and not refreshing.`);
  }
  const config = {
    method,
    headers
  };
  if (body) {
    config.body = JSON.stringify(body);
  }
  try {
    console.log(`API Request: ${method} ${url}`);
    const response = await fetch(url, config);
    if (response.status === 308 || response.status === 307) {
      console.warn(`Redirect detected (${response.status}) for ${url}. This may indicate a URL format issue.`);
      const redirectUrl = response.headers.get("Location");
      if (redirectUrl) {
        console.log(`Redirect URL: ${redirectUrl}`);
      }
    }
    if (response.status === 204) {
      return null;
    }
    let responseData;
    const contentType = response.headers.get("content-type");
    if (contentType && contentType.includes("application/json")) {
      responseData = await response.json();
    } else {
      responseData = await response.text() || response.statusText || null;
    }
    if (!response.ok) {
      const error = new Error(
        responseData?.message || response.statusText || "API Request Failed"
      );
      error.response = response;
      error.status = response.status;
      error.data = responseData;
      console.error(`API Error: ${method} ${url} returned ${response.status} ${response.statusText}`);
      throw error;
    }
    return responseData;
  } catch (error) {
    const originalRequestConfig = configArgs;
    if (error.status === 401 && !originalRequestConfig.isRetry && originalRequestConfig.requiresAuth) {
      if (!isRefreshing) {
        isRefreshing = true;
        try {
          console.log("Access token expired or invalid. Attempting to refresh...");
          const newAccessToken = await authService.refreshAccessToken();
          if (newAccessToken) {
            console.log("Token refreshed successfully. Retrying original request and processing queue.");
            processQueue(null, newAccessToken);
            return request(
              originalRequestConfig.endpoint,
              originalRequestConfig.method,
              originalRequestConfig.body,
              originalRequestConfig.requiresAuth,
              originalRequestConfig.customHeaders,
              true
              // Mark as retry
            );
          } else {
            console.error("Failed to refresh token, newAccessToken is null. User should be logged out.");
            processQueue(error, null);
            throw error;
          }
        } catch (refreshError) {
          console.error("Error during token refresh process:", refreshError);
          processQueue(refreshError, null);
          throw refreshError;
        } finally {
          isRefreshing = false;
        }
      } else {
        console.log("Token refresh in progress. Queuing request to:", originalRequestConfig.endpoint);
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject, config: originalRequestConfig });
        });
      }
    }
    console.error(`API Error (${method} ${endpoint}):`, error.status, error.message, error.data || error);
    throw error;
  }
}
const api = {
  get: (endpoint, requiresAuth = true, customHeaders = {}) => request(endpoint, "GET", null, requiresAuth, customHeaders),
  post: (endpoint, body, requiresAuth = true, customHeaders = {}) => request(endpoint, "POST", body, requiresAuth, customHeaders),
  put: (endpoint, body, requiresAuth = true, customHeaders = {}) => request(endpoint, "PUT", body, requiresAuth, customHeaders),
  delete: (endpoint, requiresAuth = true, customHeaders = {}) => request(endpoint, "DELETE", null, requiresAuth, customHeaders),
  patch: (endpoint, body, requiresAuth = true, customHeaders = {}) => request(endpoint, "PATCH", body, requiresAuth, customHeaders)
};
async function loginUser(credentials) {
  try {
    const response = await api.post("/auth/login", credentials, false);
    if (response.access_token && response.refresh_token) {
      authStore.login({ access_token: response.access_token, refresh_token: response.refresh_token });
      await fetchUserProfile();
    } else {
      throw new Error("Login response did not include tokens.");
    }
  } catch (error) {
    console.error("AuthService: Login failed", error);
    authStore.logout();
    throw error;
  }
}
async function registerUser(userData) {
  try {
    console.log("AuthService: Starting user registration process");
    if (!userData.username || !userData.email || !userData.password) {
      console.error("AuthService: Missing required registration fields");
      throw new Error("Missing required fields (username, email, password)");
    }
    if (userData.password.length < 8) {
      console.error("AuthService: Password too short");
      throw new Error("Password must be at least 8 characters long");
    }
    console.log("AuthService: Sending registration request to API");
    const response = await api.post("/auth/register", userData, false);
    console.log("AuthService: Registration successful", {
      userId: response.user?.id,
      username: response.user?.username,
      message: response.message
    });
    return response;
  } catch (error) {
    console.error("AuthService: Registration failed", error);
    if (error instanceof Error) {
      console.error("AuthService: Error details:", {
        message: error.message,
        stack: error.stack,
        name: error.name
      });
      const apiError = error;
      if (apiError.status) {
        console.error("AuthService: API error details:", {
          status: apiError.status,
          data: apiError.data
        });
      }
    }
    throw error;
  }
}
async function fetchUserProfile() {
  try {
    const userProfile = await api.get("/auth/me");
    if (userProfile) {
      authStore.setUserProfile(userProfile);
      return userProfile;
    }
    return null;
  } catch (error) {
    console.error("AuthService: Failed to fetch user profile", error);
    if (error.status === 401 || error.status === 422) {
      authStore.logout();
    }
    throw error;
  }
}
async function logoutUser() {
  try {
    const currentAuthState = get(authStore);
    if (currentAuthState.accessToken) {
      await api.post("/auth/logout", {}, true);
    }
  } catch (error) {
    console.error("AuthService: Server logout failed (access token revocation)", error);
  } finally {
    authStore.logout();
  }
}
async function changePassword(payload) {
  try {
    const response = await api.post("/auth/change-password", payload, true);
    return response;
  } catch (error) {
    console.error("AuthService: Change password failed", error);
    throw error;
  }
}
async function refreshAccessToken() {
  const currentAuthState = get(authStore);
  if (!currentAuthState.refreshToken) {
    console.warn("AuthService: No refresh token available to refresh access token.");
    authStore.logout();
    return null;
  }
  try {
    const BASE_URL2 = "";
    const response = await fetch(`${BASE_URL2}/auth/refresh`, {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${currentAuthState.refreshToken}`,
        "Content-Type": "application/json"
        // Though body is empty, Content-Type might be expected
      }
      // No body is sent for this specific endpoint as per API spec
    });
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ message: response.statusText }));
      const error = new Error(errorData.message || "Token refresh failed");
      error.status = response.status;
      error.data = errorData;
      throw error;
    }
    const tokenResponse = await response.json();
    const newAccessToken = tokenResponse.access_token;
    if (newAccessToken) {
      authStore.setTokens({ accessToken: newAccessToken });
      return newAccessToken;
    } else {
      throw new Error("New access token not found in refresh response.");
    }
  } catch (error) {
    console.error("AuthService: Failed to refresh access token", error);
    if (error.status === 401 || error.status === 422) {
      authStore.logout();
    }
    throw error;
  }
}
const authService = {
  loginUser,
  registerUser,
  logoutUser,
  fetchUserProfile,
  changePassword,
  refreshAccessToken
};
export {
  api as a
};
