const pageContainer = "max-w-7xl mx-auto px-4 py-4";
const cardBase = "rounded-lg shadow-md border overflow-hidden bg-white dark:bg-gray-800";
const colorSchemes = {
  // Plan 页面 - 绿色主题
  plan: {
    border: "border-green-200 dark:border-green-800",
    text: "text-green-900 dark:text-green-100",
    icon: "text-green-500",
    hover: "hover:bg-green-100 dark:hover:bg-green-800/50",
    scrollbar: "bg-green-500"
  },
  // Doing 页面 - 蓝色主题
  doing: {
    border: "border-blue-200 dark:border-blue-800",
    text: "text-blue-900 dark:text-blue-100",
    icon: "text-blue-500",
    hover: "hover:bg-blue-100 dark:hover:bg-blue-800/50",
    scrollbar: "bg-blue-500"
  },
  // Done 页面 - 紫色主题
  done: {
    border: "border-purple-200 dark:border-purple-800",
    text: "text-purple-900 dark:text-purple-100",
    icon: "text-purple-500",
    hover: "hover:bg-purple-100 dark:hover:bg-purple-800/50",
    scrollbar: "bg-purple-500"
  }
};
const layouts = {
  // 两列布局 (1:3 比例)
  twoColumnOneThree: "grid grid-cols-1 lg:grid-cols-4 gap-6"
};
const columnSpans = {
  oneFourth: "lg:col-span-1",
  threeFourths: "lg:col-span-3"
};
const headings = {
  h1: "text-3xl font-bold mb-4 flex items-center",
  h2: "text-xl font-semibold mb-4 flex items-center",
  h3: "text-lg font-semibold mb-2"
};
const scrollArea = {
  container: "relative overflow-hidden",
  indicator: "absolute top-0 bottom-0 w-1 opacity-50 z-10"
};
function combineClasses(...classes) {
  return classes.filter(Boolean).join(" ");
}
export {
  columnSpans as a,
  colorSchemes as b,
  combineClasses as c,
  cardBase as d,
  headings as h,
  layouts as l,
  pageContainer as p,
  scrollArea as s
};
