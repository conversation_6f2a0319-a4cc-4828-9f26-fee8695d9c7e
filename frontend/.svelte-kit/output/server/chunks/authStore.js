import { d as derived, w as writable } from "./index2.js";
function getInitialState() {
  {
    return {
      user: null,
      accessToken: null,
      refreshToken: null
    };
  }
}
const { subscribe, set, update } = writable(getInitialState());
function login(tokens, userData) {
  update((state) => ({
    ...state,
    user: userData || state.user,
    // Keep existing user data if new data isn't provided immediately
    accessToken: tokens.access_token,
    refreshToken: tokens.refresh_token
  }));
}
function logout() {
  set({
    // Reset to initial empty state
    user: null,
    accessToken: null,
    refreshToken: null
  });
}
function setUserProfile(userData) {
  update((state) => ({
    ...state,
    user: userData
  }));
}
function setTokens(tokens) {
  update((state) => {
    const newState = { ...state };
    if (tokens.accessToken !== void 0) {
      newState.accessToken = tokens.accessToken;
    }
    if (tokens.refreshToken !== void 0) {
      newState.refreshToken = tokens.refreshToken;
    }
    return newState;
  });
}
derived(
  { subscribe },
  // Pass the subscribe method of the writable store
  ($authState) => !!$authState.accessToken
  // User is considered authenticated if an access token exists
);
const authStore = {
  subscribe,
  set,
  // Expose set if direct manipulation is ever needed, though methods are preferred
  update,
  // Expose update for more complex state changes if necessary
  login,
  logout,
  setUserProfile,
  setTokens
};
export {
  authStore as a
};
