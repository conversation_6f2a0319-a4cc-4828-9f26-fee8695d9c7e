import { w as writable } from "./index2.js";
const initialState = {
  notifications: []
};
const notificationWritable = writable(initialState);
function generateId() {
  return `notification-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
}
function addNotification(type, message, options = {}) {
  if (!message || typeof message !== "string") {
    console.warn("NotificationStore: 无效的消息内容", message);
    return "";
  }
  if (!["success", "error", "warning", "info"].includes(type)) {
    console.warn("NotificationStore: 无效的通知类型", type);
    type = "info";
  }
  const id = generateId();
  const notification = {
    id,
    type,
    message: message.trim(),
    duration: options.duration ?? (type === "success" ? 3e3 : type === "error" ? 5e3 : 4e3),
    dismissible: options.dismissible ?? true
  };
  try {
    notificationWritable.update((state) => {
      const maxNotifications = 5;
      let notifications = [...state.notifications, notification];
      if (notifications.length > maxNotifications) {
        notifications = notifications.slice(-maxNotifications);
      }
      return {
        ...state,
        notifications
      };
    });
    if (notification.duration && notification.duration > 0) {
      setTimeout(() => {
        removeNotification(id);
      }, notification.duration);
    }
  } catch (error2) {
    console.error("NotificationStore: 添加通知时发生错误", error2);
    return "";
  }
  return id;
}
function removeNotification(id) {
  if (!id || typeof id !== "string") {
    console.warn("NotificationStore: 无效的通知ID", id);
    return;
  }
  try {
    notificationWritable.update((state) => ({
      ...state,
      notifications: state.notifications.filter((n) => n.id !== id)
    }));
  } catch (error2) {
    console.error("NotificationStore: 移除通知时发生错误", error2);
  }
}
function clearAllNotifications() {
  try {
    notificationWritable.update((state) => ({
      ...state,
      notifications: []
    }));
  } catch (error2) {
    console.error("NotificationStore: 清除所有通知时发生错误", error2);
  }
}
const success = (message, options) => addNotification("success", message, options);
const error = (message, options) => addNotification("error", message, options);
const warning = (message, options) => addNotification("warning", message, options);
const info = (message, options) => addNotification("info", message, options);
const notificationStore = {
  subscribe: notificationWritable.subscribe,
  addNotification,
  removeNotification,
  clearAllNotifications,
  success,
  error,
  warning,
  info
};
export {
  notificationStore as n
};
