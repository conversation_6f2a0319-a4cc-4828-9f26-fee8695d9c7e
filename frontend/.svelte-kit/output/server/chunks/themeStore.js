import { d as derived, w as writable } from "./index2.js";
function getInitialState() {
  return { darkMode: false, customBackground: null };
}
const { subscribe, set, update } = writable(getInitialState());
const themeStore = {
  subscribe,
  set,
  update,
  toggleDarkMode: () => {
    update((settings) => ({ ...settings, darkMode: !settings.darkMode }));
  },
  setCustomBackground: (url) => {
    update((settings) => ({ ...settings, customBackground: url }));
  }
};
derived(
  themeStore,
  ($settings) => $settings.darkMode
);
derived(
  themeStore,
  ($settings) => $settings.customBackground
);
