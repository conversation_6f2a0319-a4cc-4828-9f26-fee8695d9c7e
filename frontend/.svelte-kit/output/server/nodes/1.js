

export const index = 1;
let component_cache;
export const component = async () => component_cache ??= (await import('../entries/fallbacks/error.svelte.js')).default;
export const imports = ["_app/immutable/nodes/1.C5EuvAqy.js","_app/immutable/chunks/CEORzCZH.js","_app/immutable/chunks/B9CKrN7X.js","_app/immutable/chunks/Dj8TIuzp.js","_app/immutable/chunks/Cc0s-Eqn.js","_app/immutable/chunks/ZU85TcGt.js","_app/immutable/chunks/AJOKwFHf.js","_app/immutable/chunks/zBAGAr4e.js","_app/immutable/chunks/D6jnoBFN.js"];
export const stylesheets = [];
export const fonts = [];
