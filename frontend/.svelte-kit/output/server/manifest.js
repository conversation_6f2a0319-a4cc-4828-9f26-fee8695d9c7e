export const manifest = (() => {
function __memo(fn) {
	let value;
	return () => value ??= (value = fn());
}

return {
	appDir: "_app",
	appPath: "_app",
	assets: new Set(["favicon.png","images/default-background.jpg","images/icons/add.svg","images/icons/anchor.svg","images/icons/close.svg","images/icons/delete.svg","images/icons/doing.svg","images/icons/done.svg","images/icons/edit.svg","images/icons/error.svg","images/icons/info.svg","images/icons/menu.svg","images/icons/plan.svg","images/icons/search.svg","images/icons/success.svg","images/icons/todo.svg","images/icons/warning.svg","js/anchor-button.js"]),
	mimeTypes: {".png":"image/png",".jpg":"image/jpeg",".svg":"image/svg+xml",".js":"text/javascript"},
	_: {
		client: {start:"_app/immutable/entry/start.C2gpVlfF.js",app:"_app/immutable/entry/app.DxdsKldT.js",imports:["_app/immutable/entry/start.C2gpVlfF.js","_app/immutable/chunks/zBAGAr4e.js","_app/immutable/chunks/B9CKrN7X.js","_app/immutable/chunks/D6jnoBFN.js","_app/immutable/entry/app.DxdsKldT.js","_app/immutable/chunks/B9CKrN7X.js","_app/immutable/chunks/Cc0s-Eqn.js","_app/immutable/chunks/CEORzCZH.js","_app/immutable/chunks/C9zVtFY0.js","_app/immutable/chunks/B16-Q6Ob.js","_app/immutable/chunks/BIYeehul.js","_app/immutable/chunks/D6jnoBFN.js"],stylesheets:[],fonts:[],uses_env_dynamic_public:false},
		nodes: [
			__memo(() => import('./nodes/0.js')),
			__memo(() => import('./nodes/1.js')),
			__memo(() => import('./nodes/2.js')),
			__memo(() => import('./nodes/3.js')),
			__memo(() => import('./nodes/4.js')),
			__memo(() => import('./nodes/5.js')),
			__memo(() => import('./nodes/6.js')),
			__memo(() => import('./nodes/7.js')),
			__memo(() => import('./nodes/8.js')),
			__memo(() => import('./nodes/9.js')),
			__memo(() => import('./nodes/10.js'))
		],
		routes: [
			{
				id: "/",
				pattern: /^\/$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 3 },
				endpoint: null
			},
			{
				id: "/(app)/anchor",
				pattern: /^\/anchor\/?$/,
				params: [],
				page: { layouts: [0,2,], errors: [1,,], leaf: 4 },
				endpoint: null
			},
			{
				id: "/(app)/doing",
				pattern: /^\/doing\/?$/,
				params: [],
				page: { layouts: [0,2,], errors: [1,,], leaf: 5 },
				endpoint: null
			},
			{
				id: "/(app)/done",
				pattern: /^\/done\/?$/,
				params: [],
				page: { layouts: [0,2,], errors: [1,,], leaf: 6 },
				endpoint: null
			},
			{
				id: "/login",
				pattern: /^\/login\/?$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 8 },
				endpoint: null
			},
			{
				id: "/(app)/plan",
				pattern: /^\/plan\/?$/,
				params: [],
				page: { layouts: [0,2,], errors: [1,,], leaf: 7 },
				endpoint: null
			},
			{
				id: "/register",
				pattern: /^\/register\/?$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 9 },
				endpoint: null
			},
			{
				id: "/ui-components",
				pattern: /^\/ui-components\/?$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 10 },
				endpoint: null
			}
		],
		prerendered_routes: new Set([]),
		matchers: async () => {
			
			return {  };
		},
		server_assets: {}
	}
}
})();
