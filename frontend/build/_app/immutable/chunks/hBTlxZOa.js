import{c as be,a as c,t as w,n as oe}from"./CEORzCZH.js";import{p as ne,f as ie,a as le,i as n,P as U,c as g,s as k,r as u,t as N,Q as re,x as s,O as ye,n as se}from"./B9CKrN7X.js";import{d as de,s as ae,e as $}from"./Cc0s-Eqn.js";import{i as h}from"./C9zVtFY0.js";import{a as ce,s as pe}from"./DUXSSeDn.js";import{t as ge,a as ke,b as he}from"./D8moldTO.js";import{p as A,r as ue,b as xe}from"./B16-Q6Ob.js";import{s as we}from"./BIuBsydj.js";import{s as _e,a as Be}from"./BIYeehul.js";import{c as Ce,t as me}from"./BCVqPHae.js";import{o as Le}from"./D6jnoBFN.js";async function Ee(f,r,a){s(r,!1),await ye(),a("dismiss")}var Me=oe('<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="12" y1="16" x2="12" y2="12"></line><line x1="12" y1="8" x2="12.01" y2="8"></line></svg>'),je=oe('<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path><line x1="12" y1="9" x2="12" y2="13"></line><line x1="12" y1="17" x2="12.01" y2="17"></line></svg>'),De=oe('<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path><polyline points="22 4 12 14.01 9 11.01"></polyline></svg>'),Se=w('<div class="flex-shrink-0 mr-3"><!></div>'),Pe=w('<h3 class="text-sm font-medium mb-1"> </h3>'),Fe=w('<button type="button" class="ml-auto -mx-1.5 -my-1.5 bg-transparent text-current p-1.5 inline-flex items-center justify-center rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-transparent" aria-label="Dismiss"><svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg></button>'),ze=w('<div><div class="flex"><!> <div class="flex-1"><!> <div class="text-sm"> </div></div> <!></div></div>');function rr(f,r){ne(r,!0);const a=(C,p)=>{const S=new CustomEvent(C,{detail:p});document.dispatchEvent(S)};let x=A(r,"variant",3,"default"),E=A(r,"title",3,""),Q=A(r,"dismissible",3,!1),W=A(r,"icon",3,!0),M=A(r,"class",3,""),j=ue(r,["$$slots","$$events","$$legacy","variant","title","dismissible","icon","class","children"]),_=U(!0);const B={default:"bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300 border-gray-200 dark:border-gray-700",primary:"bg-primary-50 text-primary-800 dark:bg-primary-900/20 dark:text-primary-300 border-primary-200 dark:border-primary-800",secondary:"bg-secondary-50 text-secondary-800 dark:bg-secondary-900/20 dark:text-secondary-300 border-secondary-200 dark:border-secondary-800",danger:"bg-danger-50 text-danger-800 dark:bg-danger-900/20 dark:text-danger-300 border-danger-200 dark:border-danger-800",success:"bg-success-50 text-success-800 dark:bg-success-900/20 dark:text-success-300 border-success-200 dark:border-success-800",warning:"bg-warning-50 text-warning-800 dark:bg-warning-900/20 dark:text-warning-300 border-warning-200 dark:border-warning-800",info:"bg-info-50 text-info-800 dark:bg-info-900/20 dark:text-info-300 border-info-200 dark:border-info-800"},b={default:"info",primary:"info",secondary:"info",danger:"alert-triangle",success:"check-circle",warning:"alert-triangle",info:"info"},q=re(()=>B[x()]),K=re(()=>`
    relative rounded-lg border p-4
    ${n(q)}
    ${M()}
  `),O=re(()=>b[x()]);var V=be(),G=ie(V);{var D=C=>{var p=ze();let S;var J=g(p),e=g(J);{var l=o=>{var m=Se(),F=g(m);{var T=t=>{var v=Me();c(t,v)},X=(t,v)=>{{var z=I=>{var Z=je();c(I,Z)},Y=(I,Z)=>{{var ve=ee=>{var fe=De();c(ee,fe)};h(I,ee=>{n(O)==="check-circle"&&ee(ve)},Z)}};h(t,I=>{n(O)==="alert-triangle"?I(z):I(Y,!1)},v)}};h(F,t=>{n(O)==="info"?t(T):t(X,!1)})}u(m),c(o,m)};h(e,o=>{W()&&o(l)})}var y=k(e,2),L=g(y);{var i=o=>{var m=Pe(),F=g(m,!0);u(m),N(()=>ae(F,E())),c(o,m)};h(L,o=>{E()&&o(i)})}var d=k(L,2),H=g(d,!0);u(d),u(y);var R=k(y,2);{var P=o=>{var m=Fe();m.__click=[Ee,_,a],c(o,m)};h(R,o=>{Q()&&o(P)})}u(J),u(p),N(()=>{S=ce(p,S,{class:n(K),role:"alert",...j}),ae(H,r.children)}),ge(3,p,()=>ke,()=>({duration:200})),c(C,p)};h(G,C=>{n(_)&&C(D)})}c(f,V),le()}de(["click"]);function te(f,r){s(r,!n(r))}function Ie(f,r){const a=f.target;a.files&&a.files[0]&&r(a.files[0])}function Ue(f,r,a){try{s(r,null),me.setCustomBackground(null),document.documentElement.style.setProperty("--custom-background","none"),document.documentElement.classList.remove("has-custom-bg"),document.body.classList.remove("has-background"),document.body.classList.remove("dark-with-background"),document.body.style.backgroundColor="",document.documentElement.style.backgroundColor="",console.log("Background cleared successfully"),setTimeout(()=>{const x=localStorage.getItem("app_theme_settings");if(x){const E=JSON.parse(x);console.log("Background in localStorage after clearing:",E.customBackground)}},100)}catch(x){console.error("Error clearing background:",x),s(a,"Failed to clear background.")}}var Ae=(f,r)=>{var a;return(a=n(r))==null?void 0:a.click()},Ne=(f,r)=>{var a;return f.key==="Enter"&&((a=n(r))==null?void 0:a.click())},Oe=w('<div class="relative w-full h-32 mb-2"><img alt="Background preview" class="w-full h-full object-cover rounded"></div> <p class="text-xs text-gray-500 dark:text-gray-400">Click or drag to change image</p>',1),Re=w('<svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-10 w-10 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path></svg> <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Click to select or drag an image here</p> <p class="text-xs text-gray-400 dark:text-gray-500">PNG, JPG, GIF up to 5MB</p>',1),Te=w('<div class="text-sm text-red-500 mb-3"> </div>'),Ve=w('<div class="absolute right-0 mt-2 w-72 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50 p-4"><div class="flex justify-between items-center mb-3"><h3 class="text-sm font-medium text-gray-900 dark:text-gray-100">Custom Background</h3> <button class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200" aria-label="Close panel"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12"></path></svg></button></div> <div role="button" tabindex="0"><input type="file" accept="image/*" class="hidden"> <!></div> <!> <div class="flex justify-between"><button type="button" class="inline-flex items-center justify-center font-medium rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed px-3 py-1.5 text-xs border border-gray-300 bg-transparent text-gray-700 hover:bg-gray-50 focus:ring-gray-400 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-800">Clear</button> <button type="button" class="inline-flex items-center justify-center font-medium rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed px-3 py-1.5 text-xs bg-primary-500 text-white hover:bg-primary-600 focus:ring-primary-400 dark:bg-primary-600 dark:hover:bg-primary-700">Done</button></div></div>'),Ge=w('<div><button type="button" class="inline-flex items-center justify-center font-medium rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed px-3 py-1.5 text-xs border border-gray-300 bg-transparent text-gray-700 hover:bg-gray-50 focus:ring-gray-400 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-800 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path></svg> Background</button> <!></div>');function tr(f,r){ne(r,!0);const[a,x]=Be(),E=()=>_e(Ce,"$customBackground",a);let Q=A(r,"class",3,""),W=ue(r,["$$slots","$$events","$$legacy","class"]),M=U(!1),j=U(null),_=U(!1),B=U(null),b=U(null);Le(()=>{s(B,E(),!0)});function q(e){e.preventDefault(),s(_,!0)}function K(e){e.preventDefault(),s(_,!1)}function O(e){e.preventDefault(),s(_,!0)}function V(e){var l;e.preventDefault(),s(_,!1),(l=e.dataTransfer)!=null&&l.files&&e.dataTransfer.files[0]&&G(e.dataTransfer.files[0])}function G(e){if(s(b,null),console.log("Processing file:",e.name,e.type,e.size),!e.type.startsWith("image/")){s(b,"Please select an image file."),console.error("Invalid file type:",e.type);return}if(e.size>5*1024*1024){s(b,"Image size should be less than 5MB."),console.error("File too large:",e.size);return}try{const l=URL.createObjectURL(e);s(B,l,!0),console.log("Preview URL created:",l);const y=new FileReader;y.onload=L=>{var i;try{const d=(i=L.target)==null?void 0:i.result;if(!d)throw new Error("Failed to convert image to base64");console.log("Base64 string created (length):",d.length),me.setCustomBackground(d),console.log("Background set in theme store"),document.documentElement.style.setProperty("--custom-background",`url(${d})`),console.log("Background applied to document element"),document.documentElement.classList.add("has-custom-bg"),document.body.classList.add("has-background"),document.body.style.backgroundColor="transparent",document.documentElement.style.backgroundColor="transparent",document.documentElement.classList.contains("dark")?document.body.classList.add("dark-with-background"):document.body.classList.remove("dark-with-background");const R=document.documentElement.style.getPropertyValue("--custom-background");console.log("Applied style value:",R),setTimeout(()=>{const P=localStorage.getItem("app_theme_settings");if(P){const o=JSON.parse(P);console.log("Stored background in localStorage:",o.customBackground?"exists (length: "+o.customBackground.length+")":"none")}},100)}catch(d){console.error("Error in reader.onload:",d),s(b,"Error processing image data.")}},y.onerror=L=>{console.error("Error reading file:",L),s(b,"Error processing image file.")},y.readAsDataURL(e)}catch(l){console.error("Error in processFile:",l),s(b,"Failed to process the image.")}}var D=Ge();let C;var p=g(D);p.__click=[te,M];var S=k(p,2);{var J=e=>{var l=Ve(),y=g(l),L=k(g(y),2);L.__click=[te,M],u(y);var i=k(y,2);i.__click=[Ae,j],i.__keydown=[Ne,j];var d=g(i);d.__change=[Ie,G],xe(d,t=>s(j,t),()=>n(j));var H=k(d,2);{var R=t=>{var v=Oe(),z=ie(v),Y=g(z);u(z),se(2),N(()=>pe(Y,"src",n(B))),c(t,v)},P=t=>{var v=Re();se(4),c(t,v)};h(H,t=>{n(B)?t(R):t(P,!1)})}u(i);var o=k(i,2);{var m=t=>{var v=Te(),z=g(v,!0);u(v),N(()=>ae(z,n(b))),c(t,v)};h(o,t=>{n(b)&&t(m)})}var F=k(o,2),T=g(F);T.__click=[Ue,B,b];var X=k(T,2);X.__click=[te,M],u(F),u(l),N(()=>{we(i,1,`border-2 border-dashed rounded-lg p-4 text-center cursor-pointer mb-3 ${n(_)?"border-primary-500 bg-primary-50 dark:bg-primary-900/20":"border-gray-300 dark:border-gray-600 hover:border-primary-400 dark:hover:border-primary-500"}`),T.disabled=!n(B)}),$("dragenter",i,q),$("dragleave",i,K),$("dragover",i,O),$("drop",i,V),ge(3,l,()=>he,()=>({duration:200})),c(e,l)};h(S,e=>{n(M)&&e(J)})}u(D),N(()=>C=ce(D,C,{class:`relative ${Q()}`,...W})),c(f,D),le(),x()}de(["click","keydown","change"]);export{rr as A,tr as B};
