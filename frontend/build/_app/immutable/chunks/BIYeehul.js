import{s as o,g as c}from"./D6jnoBFN.js";import{o as l,q as b,v as a,w as p,x as d,i as _}from"./B9CKrN7X.js";let s=!1,i=Symbol();function y(e,n,r){const u=r[n]??(r[n]={store:null,source:p(void 0),unsubscribe:a});if(u.store!==e&&!(i in r))if(u.unsubscribe(),u.store=e??null,e==null)u.source.v=void 0,u.unsubscribe=a;else{var t=!0;u.unsubscribe=o(e,f=>{t?u.source.v=f:d(u.source,f)}),t=!1}return e&&i in r?c(e):_(u.source)}function m(){const e={};function n(){l(()=>{for(var r in e)e[r].unsubscribe();b(e,i,{enumerable:!1,value:!0})})}return[e,n]}function w(e){var n=s;try{return s=!1,[e(),s]}finally{s=n}}export{m as a,w as c,y as s};
