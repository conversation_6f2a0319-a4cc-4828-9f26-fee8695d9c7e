import{t as S,a as p,n as we,d as Xe}from"../chunks/CEORzCZH.js";import{p as Be,c as a,r as t,s as o,i as r,P as R,t as V,a as Te,x as n,_ as Pe,d as it,Q as Ye,n as He}from"../chunks/B9CKrN7X.js";import{d as ze,s as F,r as ut,e as ct}from"../chunks/Cc0s-Eqn.js";import{i as z}from"../chunks/C9zVtFY0.js";import{s as k,c as L}from"../chunks/BIuBsydj.js";import{e as et,i as vt}from"../chunks/D8moldTO.js";import{p as _e}from"../chunks/B16-Q6Ob.js";import{a as tt,s as Se}from"../chunks/BIYeehul.js";import{g as Me,w as je,o as gt}from"../chunks/D6jnoBFN.js";import{b as Fe}from"../chunks/DNwzTOhB.js";import{a as rt}from"../chunks/CaTJm4T8.js";import{s as Ue,r as Ie}from"../chunks/DUXSSeDn.js";import{b as Le}from"../chunks/Bk_lFxuP.js";import{b as ft}from"../chunks/Cj49lUop.js";import{c as E,p as pt,l as xt,a as Qe,b as mt,d as We,h as Je,s as Ke,M as ht}from"../chunks/CtrCr3Rb.js";const De="/plans/",Ee={async getFuturePlans(){try{return await Fe.get(De)}catch(i){throw console.error("Error fetching future plans:",i),i}},async createFuturePlan(i){try{return await Fe.post(De,i)}catch(e){throw console.error("Error creating future plan:",e),e}},async getFuturePlanById(i){try{return await Fe.get(`${De}/${i}`)}catch(e){throw console.error(`Error fetching future plan with ID ${i}:`,e),e}},async updateFuturePlan(i,e){try{return await Fe.put(`${De}${i}`,e)}catch(l){throw console.error(`Error updating future plan with ID ${i}:`,l),l}},async deleteFuturePlan(i){try{await Fe.delete(`${De}${i}`)}catch(e){throw console.error(`Error deleting future plan with ID ${i}:`,e),e}}},bt=()=>{const i=je([]),e=je(!1),l=je(null);let b=null;return rt.subscribe(u=>{b=u.user?u.user.id:null}),{futurePlans:i,isLoading:e,error:l,loadFuturePlans:async()=>{e.set(!0),l.set(null);try{if(b)try{const u=await Ee.getFuturePlans();u&&u.length>0&&i.set(u)}catch(u){console.warn("API请求失败，使用模拟数据",u)}}catch(u){l.set(u.message||"加载计划失败")}finally{e.set(!1)}},addFuturePlan:async u=>{if(!b)return l.set("User not authenticated. Cannot add future plan."),null;e.set(!0),l.set(null);try{const f=await Ee.createFuturePlan(u);return f&&i.update(h=>[f,...h].sort((_,x)=>{const B=_.created_at?new Date(_.created_at).getTime():0;return(x.created_at?new Date(x.created_at).getTime():0)-B})),f}catch(f){return l.set(f.message||"Failed to add future plan."),null}finally{e.set(!1)}},updateFuturePlan:async(u,f)=>{e.set(!0),l.set(null);try{const h=await Ee.updateFuturePlan(u,f);return h&&i.update(_=>_.map(x=>x.id===u?h:x).sort((x,B)=>{const T=x.created_at?new Date(x.created_at).getTime():0;return(B.created_at?new Date(B.created_at).getTime():0)-T})),h}catch(h){return l.set(h.message||"Failed to update future plan."),null}finally{e.set(!1)}},deleteFuturePlan:async u=>{e.set(!0),l.set(null);try{return await Ee.deleteFuturePlan(u),i.update(f=>f.filter(h=>h.id!==u)),!0}catch(f){return l.set(f.message||"Failed to delete future plan."),!1}finally{e.set(!1)}},getFuturePlanById:u=>Me(i).find(f=>f.id===u)}},j=bt();rt.subscribe(i=>{if(i.user){const e=Me(j.isLoading),l=Me(j.futurePlans);!e&&l.length===0&&j.loadFuturePlans()}else j.futurePlans.set([])});var _t=(i,e,l)=>e()(l.futurePlan),wt=(i,e,l)=>i.key==="Enter"&&e()(l.futurePlan),yt=(i,e,l)=>{i.stopPropagation(),e()(l.futurePlan)},kt=(i,e)=>{i.stopPropagation(),e()},Ct=we('<svg aria-hidden="true" role="status" class="inline w-4 h-4 text-red-500 animate-spin" viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z" fill="#E5E7EB"></path><path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0492C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z" fill="currentColor"></path></svg>'),Pt=we('<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>'),Ft=S('<p class="text-xs text-gray-500 dark:text-gray-400 mt-1"> </p>'),Dt=S('<div class="mb-3"><h5 class="text-sm font-semibold text-gray-600 dark:text-gray-400 mb-1">Goal Type:</h5> <span class="px-2 py-0.5 text-xs font-medium text-green-700 bg-green-100 rounded-full dark:bg-green-900 dark:text-green-300"> </span></div>'),Mt=S('<div class="bg-white dark:bg-gray-800 shadow-md rounded-lg border border-green-200 dark:border-green-800 overflow-hidden cursor-pointer hover:shadow-lg transition-shadow mb-3" role="button" tabindex="0"><div class="border-b border-green-100 dark:border-green-800 p-4"><div class="flex justify-between items-center"><h4 class="text-lg font-semibold text-green-700 dark:text-green-300"> </h4> <div class="flex space-x-1"><button class="p-1.5 text-sm font-medium text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300 focus:outline-none focus:ring-2 focus:ring-green-500 rounded-md"><svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor"><path d="M17.414 2.586a2 2 0 00-2.828 0L7 10.172V13h2.828l7.586-7.586a2 2 0 000-2.828z"></path><path fill-rule="evenodd" d="M2 6a2 2 0 012-2h4a1 1 0 010 2H4v10h10v-4a1 1 0 112 0v4a2 2 0 01-2 2H4a2 2 0 01-2-2V6z" clip-rule="evenodd"></path></svg></button> <button class="p-1.5 text-sm font-medium text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 focus:outline-none focus:ring-2 focus:ring-red-500 rounded-md disabled:opacity-50"><!></button></div></div> <!></div> <div class="p-4"><div class="mb-3"><h5 class="text-sm font-semibold text-gray-600 dark:text-gray-400 mb-1">Status:</h5> <span> </span></div> <!></div> <div class="px-4 py-2 bg-green-50 dark:bg-green-900/20 text-xs text-gray-500 dark:text-gray-400 border-t border-green-100 dark:border-green-800"><div class="flex justify-between"><span> </span> <span> <!></span></div></div></div>');function Lt(i,e){Be(e,!0);let l=_e(e,"onEdit",3,()=>{}),b=_e(e,"onClick",3,()=>{}),C=R(!1);async function s(){if(confirm(`Are you sure you want to delete the plan: "${e.futurePlan.title}"? This action cannot be undone.`)){n(C,!0);try{await j.deleteFuturePlan(e.futurePlan.id)}catch(c){alert(`Failed to delete plan: ${c.message||"Unknown error"}`),console.error("Error deleting future plan:",c)}finally{n(C,!1)}}}function A(c){if(!c)return"Not set";try{const m=new Date(c+"T00:00:00");return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"long",day:"numeric"}).format(m)}catch{return c}}function ee(c){return c?c.charAt(0).toUpperCase()+c.slice(1):"N/A"}const P={active:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",achieved:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",deferred:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",abandoned:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300"};var u=Mt();u.__click=[_t,b,e],u.__keydown=[wt,b,e];var f=a(u),h=a(f),_=a(h),x=a(_,!0);t(_);var B=o(_,2),T=a(B);T.__click=[yt,l,e];var w=o(T,2);w.__click=[kt,s];var te=a(w);{var fe=c=>{var m=Ct();p(c,m)},de=c=>{var m=Pt();p(c,m)};z(te,c=>{r(C)?c(fe):c(de,!1)})}t(w),t(B),t(h);var N=o(h,2);{var U=c=>{var m=Ft(),ae=a(m);t(m),V(ce=>F(ae,`Target date: ${ce??""}`),[()=>A(e.futurePlan.target_date)]),p(c,m)};z(N,c=>{e.futurePlan.target_date&&c(U)})}t(f);var O=o(f,2),Q=a(O),I=o(a(Q),2),q=a(I,!0);t(I),t(Q);var K=o(Q,2);{var ie=c=>{var m=Dt(),ae=o(a(m),2),ce=a(ae,!0);t(ae),t(m),V(()=>F(ce,e.futurePlan.goal_type)),p(c,m)};z(K,c=>{e.futurePlan.goal_type&&c(ie)})}t(O);var $=o(O,2),pe=a($),X=a(pe),me=a(X);t(X);var re=o(X,2),ue=a(re),xe=o(ue);{var he=c=>{var m=Xe();V(ae=>F(m,`(Updated: ${ae??""})`),[()=>A(e.futurePlan.updated_at)]),p(c,m)};z(xe,c=>{e.futurePlan.updated_at&&e.futurePlan.updated_at!==e.futurePlan.created_at&&c(he)})}t(re),t(pe),t($),t(u),V((c,m)=>{Ue(u,"aria-label",`View details for plan: ${e.futurePlan.title??""}`),F(x,e.futurePlan.title),Ue(T,"aria-label",`Edit plan: ${e.futurePlan.title??""}`),w.disabled=r(C),Ue(w,"aria-label",`Delete plan: ${e.futurePlan.title??""}`),k(I,1,`px-2 py-0.5 text-xs font-medium rounded-full ${P[e.futurePlan.status]||"bg-gray-100 text-gray-800 dark:bg-gray-800/80 dark:text-gray-300"}`),F(q,c),F(me,`ID: ${e.futurePlan.id??""}`),F(ue,`${m??""} `)},[()=>ee(e.futurePlan.status),()=>A(e.futurePlan.created_at)]),p(i,u),Te()}ze(["click","keydown"]);var Et=S('<div class="text-center py-6"><div role="status" class="flex justify-center items-center"><svg aria-hidden="true" class="w-8 h-8 text-green-200 animate-spin dark:text-green-700 fill-green-600" viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z" fill="currentColor"></path><path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0492C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z" fill="lightgray"></path></svg> <span class="sr-only">Loading...</span></div> <p class="mt-2 text-sm text-green-600 dark:text-green-400">Loading plans...</p></div>'),St=()=>j.loadFuturePlans(),Bt=S('<div class="p-3 mx-2 mb-3 text-xs text-red-700 bg-red-100 rounded-lg dark:bg-red-200 dark:text-red-800 text-center" role="alert"><span class="font-medium">Error:</span> <button class="ml-2 px-2 py-1 text-xs font-medium text-white bg-green-600 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500">Retry</button></div>'),Tt=S('<div class="text-center py-6"><svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-10 w-10 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1"><path stroke-linecap="round" stroke-linejoin="round" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path></svg> <h3 class="mt-2 text-sm font-medium text-green-900 dark:text-green-100">No plans yet</h3> <p class="mt-1 text-xs text-green-600 dark:text-green-400">Click + to add one</p></div>'),zt=S('<div class="space-y-2 px-2"></div>'),At=S('<div class="py-2"><!></div>');function Vt(i,e){Be(e,!0);const[l,b]=tt(),C=()=>Se(f,"$isLoading",l),s=()=>Se(h,"$error",l),A=()=>Se(u,"$futurePlans",l);let ee=_e(e,"onEditFuturePlan",3,w=>{}),P=_e(e,"onSelectPlan",3,w=>{});const u=j.futurePlans,f=j.isLoading,h=j.error;gt(()=>{j.loadFuturePlans();const w=setTimeout(()=>{C()&&(console.warn("Loading timeout reached, resetting loading state"),j.isLoading.set(!1),s()||j.error.set("加载超时，请重试"))},5e3);return()=>{clearTimeout(w)}});var _=At(),x=a(_);{var B=w=>{var te=Et();p(w,te)},T=(w,te)=>{{var fe=N=>{var U=Bt(),O=o(a(U)),Q=o(O);Q.__click=[St],t(U),V(()=>F(O,` ${s()??""} `)),p(N,U)},de=(N,U)=>{{var O=I=>{var q=Tt();p(I,q)},Q=I=>{var q=zt();et(q,5,A,K=>K.id,(K,ie)=>{Lt(K,{get futurePlan(){return r(ie)},onEdit:$=>ee()($),onClick:$=>P()($)})}),t(q),p(I,q)};z(N,I=>{A().length===0?I(O):I(Q,!1)},U)}};z(w,N=>{s()?N(fe):N(de,!1)},te)}};z(x,w=>{C()&&A().length===0?w(B):w(T,!1)})}t(_),p(i,_),Te(),b()}ze(["click"]);function jt(i,e,l,b,C){e()?l()():(b(),n(C,null))}var Ut=S('<div class="p-3 mb-4 text-sm text-red-700 bg-red-100 rounded-lg dark:bg-red-200 dark:text-red-800" role="alert"><span class="font-medium">Error:</span> </div>'),It=S('<div class="p-3 mb-4 text-sm text-green-700 bg-green-100 rounded-lg dark:bg-green-200 dark:text-green-800" role="alert"><span class="font-medium">Success:</span> </div>'),Ht=S("<option> </option>"),Zt=we('<svg aria-hidden="true" role="status" class="inline w-4 h-4 me-3 text-white animate-spin" viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z" fill="#E5E7EB"></path><path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0492C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z" fill="currentColor"></path></svg> Processing...',1),Nt=S('<form class="space-y-6"><!> <!> <div class="mb-6"><label for="fp-title" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Title <span class="text-red-500">*</span></label> <input type="text" id="fp-title" required class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-green-500 focus:border-green-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-green-500 dark:focus:border-green-500" placeholder="Enter a title for your future plan..."></div> <div class="mb-6"><label for="fp-description" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Description <span class="text-red-500">*</span></label> <textarea id="fp-description" rows="4" required class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-green-500 focus:border-green-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-green-500 dark:focus:border-green-500" placeholder="Describe your future plan or goal in detail..."></textarea></div> <div class="mb-6"><label for="fp-status" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Status</label> <select id="fp-status" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-green-500 focus:border-green-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-green-500 dark:focus:border-green-500"></select></div> <div class="mb-6"><label for="fp-goal_type" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Goal Type (Optional)</label> <input type="text" id="fp-goal_type" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-green-500 focus:border-green-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-green-500 dark:focus:border-green-500" placeholder="e.g., Career, Personal, Financial"></div> <div class="mb-6"><label for="fp-target_date" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Target Date (Optional)</label> <input type="date" id="fp-target_date" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-green-500 focus:border-green-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-green-500 dark:focus:border-green-500"></div> <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700"><button type="button" class="py-2.5 px-5 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-green-700 focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"> </button> <button type="submit" class="text-white bg-green-600 hover:bg-green-700 focus:ring-4 focus:outline-none focus:ring-green-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-green-600 dark:hover:bg-green-700 dark:focus:ring-green-800 disabled:opacity-50"><!></button></div></form>');function Ot(i,e){var H,Z,ne,Y,le;Be(e,!0);const[l,b]=tt(),C=()=>Se(te,"$isLoadingStore",l);let s=_e(e,"futurePlan",3,null),A=_e(e,"onSave",3,d=>{}),ee=_e(e,"onCancel",3,()=>{}),P=R(Pe(((H=s())==null?void 0:H.title)||"")),u=R(Pe(((Z=s())==null?void 0:Z.description)||"")),f=R(Pe(((ne=s())==null?void 0:ne.goal_type)||"")),h=R(Pe(((Y=s())==null?void 0:Y.target_date)||"")),_=R(Pe(((le=s())==null?void 0:le.status)||"active")),x=R(null),B=R(null),T=R(!1);const w=["active","achieved","deferred","abandoned"],te=j.isLoading;let fe=Ye(C);it(()=>{s()?(n(P,s().title||"",!0),n(u,s().description||"",!0),n(f,s().goal_type||"",!0),n(h,s().target_date||"",!0),n(_,s().status||"active",!0),n(x,null),n(B,null)):(n(P,""),n(u,""),n(f,""),n(h,""),n(_,"active"))});async function de(){if(n(x,null),n(B,null),n(T,!0),!r(P).trim()){n(x,"Title cannot be empty."),n(T,!1);return}if(!r(u).trim()){n(x,"Description cannot be empty."),n(T,!1);return}if(s()&&s().id){const d={title:r(P).trim(),description:r(u).trim(),goal_type:r(f).trim()||null,target_date:r(h)||null,status:r(_)};try{const g=await j.updateFuturePlan(s().id,d);g?(n(B,"Future plan updated successfully!"),A()(g)):n(x,Me(j.error)||"Failed to update future plan.",!0)}catch(g){n(x,g.message||"An unknown error occurred while updating.",!0)}}else{const d={title:r(P).trim(),description:r(u).trim(),goal_type:r(f).trim()||void 0,target_date:r(h)||void 0,status:r(_)};try{const g=await j.addFuturePlan(d);g?(n(B,"Future plan added successfully!"),A()(g),N()):n(x,Me(j.error)||"Failed to add future plan.",!0)}catch(g){n(x,g.message||"An unknown error occurred while adding.",!0)}}n(T,!1)}function N(){n(P,""),n(u,""),n(f,""),n(h,""),n(_,"active"),n(x,null)}var U=Nt(),O=a(U);{var Q=d=>{var g=Ut(),G=o(a(g));t(g),V(()=>F(G,` ${r(x)??""}`)),p(d,g)};z(O,d=>{r(x)&&d(Q)})}var I=o(O,2);{var q=d=>{var g=It(),G=o(a(g));t(g),V(()=>F(G,` ${r(B)??""}`)),p(d,g)};z(I,d=>{r(B)&&d(q)})}var K=o(I,2),ie=o(a(K),2);Ie(ie),t(K);var $=o(K,2),pe=o(a($),2);ut(pe),t($);var X=o($,2),me=o(a(X),2);et(me,21,()=>w,vt,(d,g)=>{var G=Ht(),ve={},se=a(G,!0);t(G),V(be=>{ve!==(ve=r(g))&&(G.value=(G.__value=r(g))??""),F(se,be)},[()=>r(g).charAt(0).toUpperCase()+r(g).slice(1)]),p(d,G)}),t(me),t(X);var re=o(X,2),ue=o(a(re),2);Ie(ue),t(re);var xe=o(re,2),he=o(a(xe),2);Ie(he),t(xe);var c=o(xe,2),m=a(c);m.__click=[jt,s,ee,N,B];var ae=a(m,!0);t(m);var ce=o(m,2),Ae=a(ce);{var v=d=>{var g=Zt();He(),p(d,g)},D=d=>{var g=Xe();V(()=>F(g,s()?"Save Changes":"Add Plan")),p(d,g)};z(Ae,d=>{r(T)||r(fe)?d(v):d(D,!1)})}t(ce),t(c),t(U),V(()=>{F(ae,s()?"Cancel":"Reset"),ce.disabled=r(T)||r(fe)}),ct("submit",U,d=>{d.preventDefault(),de()}),Le(ie,()=>r(P),d=>n(P,d)),Le(pe,()=>r(u),d=>n(u,d)),ft(me,()=>r(_),d=>n(_,d)),Le(ue,()=>r(f),d=>n(f,d)),Le(he,()=>r(h),d=>n(h,d)),p(i,U),Te(),b()}ze(["click"]);async function Gt(i,e,l,b,C){if(r(e)&&confirm(`Are you sure you want to delete "${r(e).title}"? This action cannot be undone.`)){n(l,!0),n(b,null);try{await j.deleteFuturePlan(r(e).id)?(n(b,{type:"success",message:`Successfully deleted "${r(e).title}"`},!0),n(e,null),n(C,!1),setTimeout(()=>{n(b,null)},3e3)):n(b,{type:"error",message:"Failed to delete plan. Please try again."},!0)}catch(s){n(b,{type:"error",message:`Failed to delete: ${s.message||"Unknown error"}`},!0)}finally{n(l,!1)}}}var Rt=we('<svg class="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path></svg>'),qt=we('<svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path></svg>'),$t=(i,e)=>n(e,null),Qt=S('<div role="alert"><div class="flex"><div class="flex-shrink-0"><!></div> <div class="ml-3"><p class="text-sm font-medium"> </p></div> <div class="ml-auto pl-3"><div class="-mx-1.5 -my-1.5"><button><span class="sr-only">Dismiss</span> <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg></button></div></div></div></div>'),Wt=(i,e,l)=>r(e)&&l(r(e)),Jt=we('<svg class="animate-spin h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>'),Kt=we('<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 6h18"></path><path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6"></path><path d="M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path><line x1="10" y1="11" x2="10" y2="17"></line><line x1="14" y1="11" x2="14" y2="17"></line></svg>'),Xt=S('<div class="mb-6"><h3>Target Date</h3> <div class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300"><svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect><line x1="16" y1="2" x2="16" y2="6"></line><line x1="8" y1="2" x2="8" y2="6"></line><line x1="3" y1="10" x2="21" y2="10"></line></svg> </div></div>'),Yt=S('<div class="mb-6"><h3>Goal Type</h3> <div class="flex flex-wrap gap-2"><span class="px-3 py-1 text-sm font-medium text-green-700 bg-green-100 rounded-full dark:bg-green-900 dark:text-green-300"> </span></div></div>'),er=S('<div class="mb-6"><h3>Description</h3> <div class="bg-white dark:bg-gray-700 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600"><p class="text-gray-700 dark:text-gray-300 whitespace-pre-wrap"> </p></div></div>'),tr=S('<span class="ml-2"> </span>'),rr=S('<div><div class="flex justify-between items-center mb-6"><h1> </h1> <div class="flex space-x-2"><button aria-label="Edit plan"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path><path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path></svg></button> <button class="p-2 text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed" aria-label="Delete plan"><!></button></div></div> <div class="mb-6"><h3>Status</h3> <div><svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><path d="M12 6v6l4 2"></path></svg> </div></div> <!> <!> <!> <div class="mt-8 pt-4 border-t border-gray-200 dark:border-gray-700"><div class="flex justify-between text-sm text-gray-500 dark:text-gray-400"><span> </span> <span> <!></span></div></div></div>'),ar=S('<div class="flex flex-col items-center justify-center h-full"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" fill="currentColor"><path d="M64 80c-8.8 0-16 7.2-16 16l0 320c0 8.8 7.2 16 16 16l224 0 0-80c0-17.7 14.3-32 32-32l80 0 0-224c0-8.8-7.2-16-16-16L64 80zM288 480L64 480c-35.3 0-64-28.7-64-64L0 96C0 60.7 28.7 32 64 32l320 0c35.3 0 64 28.7 64 64l0 224 0 5.5c0 17-6.7 33.3-18.7 45.3l-90.5 90.5c-12 12-28.3 18.7-45.3 18.7l-5.5 0z"></path></svg> <h3>Select a Plan</h3> <p class="text-gray-600 dark:text-gray-400 text-center max-w-md">Select a plan from the list to view details, or click the "+" button to create a new plan.</p></div>'),nr=S('<div><!> <div class="flex-grow overflow-hidden"><div><div><div><div class="p-4 border-b border-gray-200 dark:border-gray-700"><div class="flex justify-between items-center"><h2><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" fill="currentColor"><path d="M64 80c-8.8 0-16 7.2-16 16l0 320c0 8.8 7.2 16 16 16l224 0 0-80c0-17.7 14.3-32 32-32l80 0 0-224c0-8.8-7.2-16-16-16L64 80zM288 480L64 480c-35.3 0-64-28.7-64-64L0 96C0 60.7 28.7 32 64 32l320 0c35.3 0 64 28.7 64 64l0 224 0 5.5c0 17-6.7 33.3-18.7 45.3l-90.5 90.5c-12 12-28.3 18.7-45.3 18.7l-5.5 0z"></path></svg> Plans</h2> <button aria-label="Add new plan"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"></path></svg></button></div></div> <div><div></div> <div><!></div></div></div></div> <div><div><div class="flex-grow overflow-hidden"><div class="h-full overflow-y-auto p-6"><!></div></div></div></div></div></div> <!></div>');function _r(i,e){Be(e,!0);const l=mt.plan;let b=R(!1),C=R(null),s=R(null),A=R(!1),ee=R(!1),P=R(null);function u(){n(C,null),n(b,!0),n(A,!1)}function f(v){n(C,v,!0),n(b,!0),n(A,!1)}function h(v){n(s,v,!0),n(A,!0),n(C,null)}function _(){n(b,!1),n(C,null)}function x(v){_(),r(s)&&r(s).id===v.id&&n(s,v,!0),r(C)||(n(s,v,!0),n(A,!0))}function B(){_()}function T(v){if(!v)return"Not set";try{const D=new Date(v+"T00:00:00");return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"long",day:"numeric"}).format(D)}catch{return v}}var w=nr(),te=a(w);{var fe=v=>{var D=Qt(),H=a(D),Z=a(H),ne=a(Z);{var Y=oe=>{var ye=Rt();p(oe,ye)},le=oe=>{var ye=qt();p(oe,ye)};z(ne,oe=>{r(P).type==="success"?oe(Y):oe(le,!1)})}t(Z);var d=o(Z,2),g=a(d),G=a(g,!0);t(g),t(d);var ve=o(d,2),se=a(ve),be=a(se);be.__click=[$t,P],t(se),t(ve),t(H),t(D),V(()=>{k(D,1,`mb-4 p-4 rounded-md ${r(P).type==="success"?"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300":"bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300"}`),F(G,r(P).message),k(be,1,`inline-flex rounded-md p-1.5 ${r(P).type==="success"?"text-green-500 hover:bg-green-100 dark:text-green-300 dark:hover:bg-green-900/50":"text-red-500 hover:bg-red-100 dark:text-red-300 dark:hover:bg-red-900/50"} focus:outline-none focus:ring-2 focus:ring-offset-2 ${r(P).type==="success"?"focus:ring-green-500":"focus:ring-red-500"}`)}),p(v,D)};z(te,v=>{r(P)&&v(fe)})}var de=o(te,2),N=a(de),U=a(N),O=a(U),Q=a(O),I=a(Q),q=a(I),K=a(q);He(),t(q);var ie=o(q,2);ie.__click=u,t(I),t(Q);var $=o(Q,2),pe=a($),X=o(pe,2),me=a(X);Vt(me,{onAddNewFuturePlan:u,onEditFuturePlan:v=>f(v),onSelectPlan:v=>h(v)}),t(X),t($),t(O),t(U);var re=o(U,2),ue=a(re),xe=a(ue),he=a(xe),c=a(he);{var m=v=>{var D=rr(),H=a(D),Z=a(H),ne=a(Z,!0);t(Z);var Y=o(Z,2),le=a(Y);le.__click=[Wt,s,f];var d=o(le,2);d.__click=[Gt,s,ee,P,A];var g=a(d);{var G=y=>{var M=Jt();p(y,M)},ve=y=>{var M=Kt();p(y,M)};z(g,y=>{r(ee)?y(G):y(ve,!1)})}t(d),t(Y),t(H);var se=o(H,2),be=a(se),oe=o(be,2),ye=o(a(oe));t(oe),t(se);var Ze=o(se,2);{var at=y=>{var M=Xt(),W=a(M),J=o(W,2),ge=o(a(J));t(J),t(M),V((ke,Ce)=>{k(W,1,ke),F(ge,` Target Date: ${Ce??""}`)},[()=>L(E("text-lg font-semibold mb-2",l.text)),()=>T(r(s).target_date)]),p(y,M)};z(Ze,y=>{r(s).target_date&&y(at)})}var Ne=o(Ze,2);{var nt=y=>{var M=Yt(),W=a(M),J=o(W,2),ge=a(J),ke=a(ge,!0);t(ge),t(J),t(M),V(Ce=>{k(W,1,Ce),F(ke,r(s).goal_type)},[()=>L(E("text-lg font-semibold mb-2",l.text))]),p(y,M)};z(Ne,y=>{r(s).goal_type&&y(nt)})}var Oe=o(Ne,2);{var lt=y=>{var M=er(),W=a(M),J=o(W,2),ge=a(J),ke=a(ge,!0);t(ge),t(J),t(M),V(Ce=>{k(W,1,Ce),F(ke,r(s).description)},[()=>L(E("text-lg font-semibold mb-2",l.text))]),p(y,M)};z(Oe,y=>{r(s).description&&y(lt)})}var Ge=o(Oe,2),Re=a(Ge),Ve=a(Re),st=a(Ve);t(Ve);var qe=o(Ve,2),$e=a(qe),ot=o($e);{var dt=y=>{var M=tr(),W=a(M);t(M),V(J=>F(W,`(Updated: ${J??""})`),[()=>T(r(s).updated_at)]),p(y,M)};z(ot,y=>{r(s).updated_at&&r(s).updated_at!==r(s).created_at&&y(dt)})}t(qe),t(Re),t(Ge),t(D),V((y,M,W,J,ge)=>{k(Z,1,y),F(ne,r(s).title||"Plan Details"),k(le,1,M),d.disabled=r(ee),k(be,1,W),k(oe,1,`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                      ${r(s).status==="active"?"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300":r(s).status==="achieved"?"bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300":r(s).status==="deferred"?"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300":"bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300"}`),F(ye,` Status: ${J??""}`),F(st,`ID: ${r(s).id??""}`),F($e,`Created: ${ge??""} `)},[()=>L(E(Je.h1,l.text)),()=>L(E("p-2 rounded-md focus:outline-none focus:ring-2",l.text,l.hover)),()=>L(E("text-lg font-semibold mb-2",l.text)),()=>r(s).status.charAt(0).toUpperCase()+r(s).status.slice(1),()=>T(r(s).created_at)]),p(v,D)},ae=v=>{var D=ar(),H=a(D),Z=o(H,2);He(2),t(D),V((ne,Y)=>{k(H,0,ne),k(Z,1,Y)},[()=>L(E("h-16 w-16 mb-4",l.icon)),()=>L(E("text-xl font-medium mb-2",l.text))]),p(v,D)};z(c,v=>{r(s)&&r(A)?v(m):v(ae,!1)})}t(he),t(xe),t(ue),t(re),t(N),t(de);var ce=o(de,2);{var Ae=v=>{const D=Ye(()=>r(C)?"Edit Future Plan":"Add New Future Plan");ht(v,{get isOpen(){return r(b)},close:_,get title(){return r(D)},modalWidth:"max-w-xl",children:(H,Z)=>{Ot(H,{get futurePlan(){return r(C)},onSave:x,onCancel:B})},$$slots:{default:!0}})};z(ce,v=>{r(b)&&v(Ae)})}t(w),V((v,D,H,Z,ne,Y,le,d,g,G,ve,se)=>{k(w,1,v),k(N,1,D),k(U,1,H),k(O,1,Z),k(q,1,ne),k(K,0,Y),k(ie,1,le),k($,1,d),k(pe,1,g),k(X,1,G),k(re,1,ve),k(ue,1,se)},[()=>L(E(pt,"h-[calc(100vh-180px)] flex flex-col")),()=>L(E(xt.twoColumnOneThree,"h-full")),()=>L(E(Qe.oneFourth,"h-full flex flex-col")),()=>L(E(We,l.border,"h-full flex flex-col")),()=>L(E(Je.h3,l.text)),()=>L(E("h-5 w-5 mr-2 inline",l.icon)),()=>L(E("p-1 text-sm rounded-md focus:outline-none focus:ring-2",l.text,l.hover)),()=>L(E(Ke.container,"flex-grow relative")),()=>L(E(Ke.indicator,"left-0",l.scrollbar)),()=>L(E("pl-3","absolute inset-0 overflow-y-auto pr-2")),()=>L(E(Qe.threeFourths,"h-full flex flex-col")),()=>L(E(We,l.border,"h-full flex flex-col"))]),p(i,w),Te()}ze(["click"]);export{_r as component};
