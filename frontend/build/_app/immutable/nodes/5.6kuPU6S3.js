import{c as Ge,a as p,t as T,n as ze,d as Je}from"../chunks/CEORzCZH.js";import{p as Te,P as F,_ as Me,f as Xe,a as Fe,c as d,i as r,s as l,r as a,n as Le,t as be,x as i,d as br,Q as Ye}from"../chunks/B9CKrN7X.js";import{d as Ae,r as ir,e as lr,s as me}from"../chunks/Cc0s-Eqn.js";import{i as z}from"../chunks/C9zVtFY0.js";import{s as U,c as re}from"../chunks/BIuBsydj.js";import{p as oe,b as Ve}from"../chunks/B16-Q6Ob.js";import{a as $e,s as he}from"../chunks/BIYeehul.js";import{d as Ze,w as fr,g as He,o as De,a as Pe}from"../chunks/D6jnoBFN.js";import{a as er}from"../chunks/CaTJm4T8.js";import{b as Re}from"../chunks/DNwzTOhB.js";import{e as nr}from"../chunks/D8moldTO.js";import{r as Se,s as ce,d as cr}from"../chunks/DUXSSeDn.js";import{b as Ce,a as mr}from"../chunks/Bk_lFxuP.js";import{b as Qe}from"../chunks/Cj49lUop.js";import{M as Ie,c as te,p as pr,l as yr,a as rr,b as xr,d as tr,h as or,s as ar}from"../chunks/CtrCr3Rb.js";import{n as _r}from"../chunks/Cy9cqOeW.js";async function hr(){try{return(await Re.get("/todo/todos")).data||[]}catch(o){throw console.error("TodoService: Failed to fetch all todos",o),o}}async function wr(o){try{return(await Re.post("/todo/todos",o)).data}catch(e){throw console.error("TodoService: Failed to create todo",e),e}}async function kr(o){try{return(await Re.get(`/todo/todos/${o}`)).data}catch(e){if(e.status===404)return console.warn(`TodoService: Todo item with ID ${o} not found.`),null;throw console.error(`TodoService: Failed to fetch todo with ID ${o}`,e),e}}async function Sr(o,e){try{return await Re.put(`/todo/todos/${o}`,e)}catch(t){throw console.error(`TodoService: Failed to update todo with ID ${o}`,t),t}}async function Tr(o){try{await Re.delete(`/todo/todos/${o}`)}catch(e){throw console.error(`TodoService: Failed to delete todo with ID ${o}`,e),e}}const Be={getAllTodos:hr,createTodo:wr,getTodoById:kr,updateTodo:Sr,deleteTodo:Tr},Fr={todos:[],isLoading:!1,error:null,maxFocusItems:3},S=fr(Fr);let dr=0;const je=()=>{dr++,S.update(o=>({...o,updateCounter:dr}))};async function Oe(){S.update(o=>({...o,isLoading:!0,error:null}));try{const e=(await Be.getAllTodos()).sort((t,c)=>t.is_current_focus&&!c.is_current_focus?-1:!t.is_current_focus&&c.is_current_focus?1:t.status!=="completed"&&c.status==="completed"?-1:t.status==="completed"&&c.status!=="completed"?1:new Date(c.created_at).getTime()-new Date(t.created_at).getTime());S.set({...He(S),todos:e,isLoading:!1,error:null})}catch(o){const e=o;console.error("TodoStore: Error fetching todos",e),S.set({...He(S),todos:[],isLoading:!1,error:e.message||"Failed to fetch todos"})}}async function Cr(o){S.update(e=>({...e,isLoading:!0,error:null}));try{const e=await Be.createTodo(o);S.update(t=>{const c=[e,...t.todos].sort((y,s)=>y.is_current_focus&&!s.is_current_focus?-1:!y.is_current_focus&&s.is_current_focus?1:new Date(s.created_at).getTime()-new Date(y.created_at).getTime());return{...t,todos:c,isLoading:!1,error:null}}),je();try{await Oe()}catch(t){console.warn("TodoStore: Failed to reload todos after add, but add was successful:",t)}return e}catch(e){const t=e;return console.error("TodoStore: Error adding todo",t),S.update(c=>({...c,isLoading:!1,error:t.message||"Failed to add todo"})),null}}async function ur(o,e){S.update(t=>({...t,isLoading:!0,error:null}));try{const t=await Be.updateTodo(o,e);S.update(c=>{const y=c.todos.map(s=>s&&s.id===o?t:s);return{...c,todos:y,isLoading:!1,error:null}}),je();try{await Oe()}catch(c){console.warn("TodoStore: Failed to reload todos after edit, but edit was successful:",c)}return t}catch(t){const c=t;return console.error("TodoStore: Error editing todo",c),S.update(y=>({...y,isLoading:!1,error:c.message||`Failed to update todo ${o}`})),null}}async function Lr(o){const e=He(S);if(!e||!e.todos)return console.error("TodoStore: Invalid state or todos array not found"),null;const t=e.todos.find(s=>s&&s.id===o);if(!t)return console.error(`TodoStore: Todo item with ID ${o} not found for toggling focus.`),S.update(s=>({...s,isLoading:!1,error:`Todo item ${o} not found.`})),null;const c=e.todos.filter(s=>s&&s.is_current_focus&&s.id!==o&&s.status!=="completed"),y=!t.is_current_focus;if(y&&c.length>=e.maxFocusItems){const s=`Cannot set more than ${e.maxFocusItems} items as current focus. Please remove another item from focus first.`;return console.warn(s),S.update(u=>({...u,isLoading:!1,error:s})),null}S.update(s=>({...s,isLoading:!0,error:null}));try{const s=await Be.updateTodo(o,{is_current_focus:y});S.update(u=>{const _=u.todos.map(g=>g&&g.id===o?s:g);return{...u,todos:_,isLoading:!1,error:null}}),je();try{await Oe()}catch(u){console.warn("TodoStore: Failed to reload todos after toggle focus, but toggle was successful:",u)}return s}catch(s){const u=s;return console.error("TodoStore: Error toggling current focus",u),S.update(_=>({..._,isLoading:!1,error:u.message||`Failed to toggle focus for item ${o}.`})),null}}async function Mr(o){S.update(e=>({...e,isLoading:!0,error:null}));try{await Be.deleteTodo(o),S.update(e=>{const t=e.todos.filter(c=>c&&c.id!==o);return{...e,todos:t,isLoading:!1,error:null}}),je();try{await Oe()}catch(e){console.warn("TodoStore: Failed to reload todos after remove, but remove was successful:",e)}}catch(e){const t=e;console.error("TodoStore: Error removing todo",t),S.update(c=>({...c,isLoading:!1,error:t.message||`Failed to delete todo ${o}`}))}}async function Ar(o,e){const t=e==="completed"?"pending":"completed",c={status:t},y=He(S);if(y&&y.todos){const s=y.todos.find(u=>u&&u.id===o);t==="completed"&&(s!=null&&s.is_current_focus)&&(c.is_current_focus=!1)}return ur(o,c)}const Dr=Ze(S,o=>!o||!o.todos?[]:[...o.todos.filter(t=>t&&t.is_current_focus&&t.status!=="completed").sort((t,c)=>new Date(c.updated_at).getTime()-new Date(t.updated_at).getTime())]),Er=Ze(S,o=>!o||!o.todos?[]:[...o.todos.filter(t=>t&&!t.is_current_focus&&t.status!=="completed").sort((t,c)=>new Date(c.created_at).getTime()-new Date(t.created_at).getTime())]),zr=Ze(S,o=>!o||!o.todos?[]:[...o.todos.filter(t=>t&&t.status==="completed").sort((t,c)=>{const y=t.completed_at?new Date(t.completed_at).getTime():0;return(c.completed_at?new Date(c.completed_at).getTime():0)-y})]),ue={subscribe:S.subscribe,set:S.set,update:S.update,loadAllTodos:Oe,addTodo:Cr,editTodo:ur,removeTodo:Mr,toggleCompleteStatus:Ar,toggleCurrentFocus:Lr};var Rr=T('<div class="mb-4 p-3 bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 border border-red-200 dark:border-red-800 rounded-md"> </div>'),Br=T('<div class="mb-4 p-3 bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300 border border-green-200 dark:border-green-800 rounded-md"> </div>'),Or=ze('<svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg> 处理中...',1),qr=T('<form class="todo-add-form"><!> <!> <div class="form-group mb-4"><label for="add-todo-title" class="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300"><span class="label-text">标题</span> <span class="text-red-500 ml-0.5">*</span></label> <input type="text" id="add-todo-title" placeholder="输入待办事项标题" required class="w-full px-4 py-3 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 disabled:bg-gray-200 dark:disabled:bg-gray-800 disabled:opacity-70 disabled:cursor-not-allowed transition-colors"></div> <div class="form-group mb-4"><label for="add-todo-description" class="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300"><span class="label-text">描述</span></label> <textarea id="add-todo-description" placeholder="添加详细描述..." rows="3" class="w-full px-4 py-3 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 disabled:bg-gray-200 dark:disabled:bg-gray-800 disabled:opacity-70 disabled:cursor-not-allowed transition-colors"></textarea></div> <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4"><div class="form-group"><label for="add-todo-due-date" class="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300"><span class="label-text">截止日期</span></label> <input type="date" id="add-todo-due-date" class="w-full px-4 py-3 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 disabled:bg-gray-200 dark:disabled:bg-gray-800 disabled:opacity-70 disabled:cursor-not-allowed transition-colors"></div> <div class="form-group"><label for="add-todo-priority" class="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300"><span class="label-text">优先级</span></label> <select id="add-todo-priority" class="w-full px-4 py-3 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 disabled:bg-gray-200 dark:disabled:bg-gray-800 disabled:opacity-70 disabled:cursor-not-allowed transition-colors"><option>低</option><option>中</option><option>高</option></select></div></div> <div class="form-group mb-6"><label class="flex items-center cursor-pointer"><input type="checkbox" class="form-checkbox h-5 w-5 text-primary-500 rounded border-gray-300 dark:border-gray-600 focus:ring-primary-500 dark:focus:ring-primary-600 dark:bg-gray-700 disabled:opacity-70 disabled:cursor-not-allowed"> <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">设为当前焦点</span></label></div> <div class="flex justify-end space-x-3"><button type="button" class="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-400 dark:focus:ring-gray-500 disabled:opacity-70 disabled:cursor-not-allowed transition-colors">取消</button> <button type="submit" class="px-4 py-2 bg-primary-500 text-white rounded-md hover:bg-primary-600 focus:outline-none focus:ring-2 focus:ring-primary-400 disabled:opacity-70 disabled:cursor-not-allowed transition-colors flex items-center"><!></button></div></form>');function Vr(o,e){Te(e,!0);const[t,c]=$e(),y=()=>he(ue,"$todoStore",t);let s=oe(e,"isOpen",3,!1),u=oe(e,"isLoading",3,!1),_=oe(e,"onAddSuccess",3,()=>{}),g=oe(e,"onCloseRequest",3,()=>{}),h=F(Me(u())),R=F(""),C=F(""),ae=F(""),k=F("medium"),B=F(!1),H=F(""),f=F(""),O=`todo-add-form-${Math.random().toString(36).substring(2)}`;function L(){i(R,""),i(C,""),i(ae,""),i(k,"medium"),i(B,!1),i(H,""),i(f,"")}async function ie(){if(!r(R).trim()){i(H,"标题为必填项。"),i(f,"");return}if(r(B)){const q=y();if(q.todos.filter(N=>N.is_current_focus&&N.status!=="completed").length>=q.maxFocusItems){i(H,`最多只能将 ${q.maxFocusItems} 个项目设为当前焦点。请先取消其他项目的焦点状态。`),i(f,"");return}}i(h,!0),i(H,""),i(f,"");const A={title:r(R).trim(),description:r(C).trim()||void 0,due_date:r(ae)||void 0,priority:r(k)};try{const q=await ue.addTodo(A);q?(i(f,`待办事项 "${q.title}" 添加成功！`),L(),_()()):i(H,y().error||"添加待办事项失败，请重试。",!0)}catch(q){const Q=q;i(H,(Q==null?void 0:Q.message)||"添加时发生意外错误。",!0),console.error("TodoAddModal handleSubmit error:",q)}finally{i(h,!1)}}function X(){L(),g()()}De(()=>{document.addEventListener("keydown",I)}),Pe(()=>{document.removeEventListener("keydown",I)});function I(A){A.key==="Escape"&&s()&&X()}var w=Ge(),m=Xe(w);{var M=A=>{Ie(A,{get isOpen(){return s()},close:X,title:"添加待办事项",modalWidth:"max-w-xl",children:(q,Q)=>{var N=qr(),le=d(N);{var n=b=>{var E=Rr(),_e=d(E,!0);a(E),be(()=>me(_e,r(H))),p(b,E)};z(le,b=>{r(H)&&b(n)})}var j=l(le,2);{var de=b=>{var E=Br(),_e=d(E,!0);a(E),be(()=>me(_e,r(f))),p(b,E)};z(j,b=>{r(f)&&b(de)})}var ve=l(j,2),$=l(d(ve),2);Se($),a(ve);var ee=l(ve,2),G=l(d(ee),2);ir(G),a(ee);var se=l(ee,2),V=d(se),W=l(d(V),2);Se(W),a(V);var ne=l(V,2),x=l(d(ne),2),J=d(x);J.value=J.__value="low";var P=l(J);P.value=P.__value="medium";var v=l(P);v.value=v.__value="high",a(x),a(ne),a(se);var D=l(se,2),Y=d(D),ge=d(Y);Se(ge),Le(2),a(Y),a(D);var pe=l(D,2),ye=d(pe);ye.__click=X;var xe=l(ye,2),we=d(xe);{var ke=b=>{var E=Or();Le(),p(b,E)},K=b=>{var E=Je("添加");p(b,E)};z(we,b=>{r(h)?b(ke):b(K,!1)})}a(xe),a(pe),a(N),be(()=>{ce(N,"id",O),$.disabled=r(h),G.disabled=r(h),W.disabled=r(h),x.disabled=r(h),ge.disabled=r(h),ye.disabled=r(h),xe.disabled=r(h)}),lr("submit",N,b=>{b.preventDefault(),ie()}),Ce($,()=>r(R),b=>i(R,b)),Ce(G,()=>r(C),b=>i(C,b)),Ce(W,()=>r(ae),b=>i(ae,b)),Qe(x,()=>r(k),b=>i(k,b)),mr(ge,()=>r(B),b=>i(B,b)),p(q,N)},$$slots:{default:!0}})};z(m,A=>{s()&&A(M)})}p(o,w),Fe(),c()}Ae(["click"]);var Hr=T('<div class="mb-4 p-3 bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 border border-red-200 dark:border-red-800 rounded-md"> </div>'),$r=T('<form class="todo-edit-form"><!> <div class="form-group mb-4"><label class="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300"><span class="label-text">标题</span> <span class="text-red-500 ml-0.5">*</span></label> <input type="text" placeholder="输入待办事项标题" required class="w-full px-4 py-3 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 disabled:bg-gray-200 dark:disabled:bg-gray-800 disabled:opacity-70 disabled:cursor-not-allowed transition-colors"></div> <div class="form-group mb-4"><label class="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300"><span class="label-text">描述</span></label> <textarea placeholder="添加详细描述..." rows="3" class="w-full px-4 py-3 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 disabled:bg-gray-200 dark:disabled:bg-gray-800 disabled:opacity-70 disabled:cursor-not-allowed transition-colors"></textarea></div> <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4"><div class="form-group"><label class="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300"><span class="label-text">截止日期</span></label> <input type="date" class="w-full px-4 py-3 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 disabled:bg-gray-200 dark:disabled:bg-gray-800 disabled:opacity-70 disabled:cursor-not-allowed transition-colors"></div> <div class="form-group"><label class="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300"><span class="label-text">优先级</span></label> <select class="w-full px-4 py-3 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 disabled:bg-gray-200 dark:disabled:bg-gray-800 disabled:opacity-70 disabled:cursor-not-allowed transition-colors"><option>低</option><option>中</option><option>高</option></select></div> <div class="form-group"><label class="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300"><span class="label-text">状态</span></label> <select class="w-full px-4 py-3 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 disabled:bg-gray-200 dark:disabled:bg-gray-800 disabled:opacity-70 disabled:cursor-not-allowed transition-colors"><option>待处理</option><option>进行中</option><option>已完成</option><option>已延期</option></select></div></div></form>');function vr(o,e){var P;Te(e,!0);let t=oe(e,"isLoading",3,!1),c=oe(e,"onSaveSuccess",3,v=>{}),y=oe(e,"onCloseModalRequest",3,()=>{}),s=F(Me(t())),u=F(""),_=F(""),g=F(""),h=F("pending"),R=F("medium"),C=F(""),ae=F(`todo-edit-form-${((P=e.todo)==null?void 0:P.id)||Math.random().toString(36).substring(2)}`);De(()=>{k()}),br(()=>{k()});function k(){e.todo&&(i(u,e.todo.title,!0),i(_,e.todo.description||"",!0),i(g,e.todo.due_date||"",!0),i(h,e.todo.status,!0),i(R,e.todo.priority,!0),i(C,""),i(ae,`todo-edit-form-${e.todo.id}`))}async function B(){if(!r(u).trim())return i(C,"标题为必填项。"),!1;i(s,!0),i(C,"");const v={title:r(u).trim(),description:r(_).trim()||null,due_date:r(g)||null,status:r(h),priority:r(R)};try{const D=await ue.editTodo(e.todo.id,v);if(D){try{_r.success(`待办事项 "${D.title}" 更新成功！`)}catch(Y){console.error("显示成功通知时发生错误:",Y)}return c()(D),!0}else return i(C,"更新待办事项失败，请重试。"),!1}catch(D){const Y=D;return i(C,(Y==null?void 0:Y.message)||"更新时发生意外错误。",!0),console.error("TodoEditForm handleSubmit error:",D),!1}finally{i(s,!1)}}function H(){y()()}function f(){const v=new Date,D=v.getFullYear(),Y=(v.getMonth()+1).toString().padStart(2,"0"),ge=v.getDate().toString().padStart(2,"0");return`${D}-${Y}-${ge}`}var O=$r(),L=d(O);{var ie=v=>{var D=Hr(),Y=d(D,!0);a(D),be(()=>me(Y,r(C))),p(v,D)};z(L,v=>{r(C)&&v(ie)})}var X=l(L,2),I=d(X),w=l(I,2);Se(w),a(X);var m=l(X,2),M=d(m),A=l(M,2);ir(A),a(m);var q=l(m,2),Q=d(q),N=d(Q),le=l(N,2);Se(le),a(Q);var n=l(Q,2),j=d(n),de=l(j,2),ve=d(de);ve.value=ve.__value="low";var $=l(ve);$.value=$.__value="medium";var ee=l($);ee.value=ee.__value="high",a(de),a(n);var G=l(n,2),se=d(G),V=l(se,2),W=d(V);W.value=W.__value="pending";var ne=l(W);ne.value=ne.__value="in_progress";var x=l(ne);x.value=x.__value="completed";var J=l(x);return J.value=J.__value="deferred",a(V),a(G),a(q),a(O),be(v=>{ce(O,"id",r(ae)),ce(I,"for",`edit-todo-title-${e.todo.id??""}`),ce(w,"id",`edit-todo-title-${e.todo.id??""}`),w.disabled=r(s),ce(M,"for",`edit-todo-description-${e.todo.id??""}`),ce(A,"id",`edit-todo-description-${e.todo.id??""}`),A.disabled=r(s),ce(N,"for",`edit-todo-due-date-${e.todo.id??""}`),ce(le,"id",`edit-todo-due-date-${e.todo.id??""}`),ce(le,"min",v),le.disabled=r(s),ce(j,"for",`edit-todo-priority-${e.todo.id??""}`),ce(de,"id",`edit-todo-priority-${e.todo.id??""}`),de.disabled=r(s),ce(se,"for",`edit-todo-status-${e.todo.id??""}`),ce(V,"id",`edit-todo-status-${e.todo.id??""}`),V.disabled=r(s)},[f]),lr("submit",O,v=>{v.preventDefault(),B()}),Ce(w,()=>r(u),v=>i(u,v)),Ce(A,()=>r(_),v=>i(_,v)),Ce(le,()=>r(g),v=>i(g,v)),Qe(de,()=>r(R),v=>i(R,v)),Qe(V,()=>r(h),v=>i(h,v)),p(o,O),Fe({handleSubmit:B,handleCancel:H})}async function Pr(o,e){return r(e)?await r(e).handleSubmit():!1}var jr=ze('<svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg> 保存中...',1),Wr=T('<div class="flex justify-end space-x-3 p-4 border-t border-gray-200 dark:border-gray-700"><button type="button" class="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-400 dark:focus:ring-gray-500 disabled:opacity-70 disabled:cursor-not-allowed transition-colors">取消</button> <button type="button" class="px-4 py-2 bg-primary-500 text-white rounded-md hover:bg-primary-600 focus:outline-none focus:ring-2 focus:ring-primary-400 disabled:opacity-70 disabled:cursor-not-allowed transition-colors flex items-center"><!></button></div>');function Kr(o,e){Te(e,!0);let t=oe(e,"isOpen",3,!1),c=oe(e,"isLoading",3,!1),y=oe(e,"onSaveSuccess",3,k=>{}),s=oe(e,"onCloseRequest",3,()=>{}),u=Me(c()),_=F(null);function g(){s()()}De(()=>{document.addEventListener("keydown",h)}),Pe(()=>{document.removeEventListener("keydown",h)});function h(k){k.key==="Escape"&&t()&&g()}var R=Ge(),C=Xe(R);{var ae=k=>{Ie(k,{get isOpen(){return t()},close:g,title:"编辑待办事项",modalWidth:"max-w-xl",children:(B,H)=>{Ve(vr(B,{get todo(){return e.todo},get isLoading(){return u},get onSaveSuccess(){return y()},onCloseModalRequest:g}),f=>i(_,f,!0),()=>r(_))},$$slots:{default:!0,footer:(B,H)=>{var f=Wr(),O=d(f);O.__click=g;var L=l(O,2);L.__click=[Pr,_];var ie=d(L);{var X=w=>{var m=jr();Le(),p(w,m)},I=w=>{var m=Je("保存更改");p(w,m)};z(ie,w=>{u?w(X):w(I,!1)})}a(L),a(f),be(()=>{O.disabled=u,L.disabled=u}),p(B,f)}}})};z(C,k=>{t()&&e.todo&&k(ae)})}p(o,R),Fe()}Ae(["click"]);var Ur=T('<div class="p-3 rounded-md bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 border border-blue-200 dark:border-blue-800 mx-2 my-2 text-center text-sm"><p>Loading tasks...</p></div>'),Nr=T('<div class="p-3 rounded-md bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 border border-red-200 dark:border-red-800 mx-2 my-2 text-center text-sm"><p class="mb-1"> </p> <button class="px-3 py-1 text-xs font-medium text-white bg-primary-500 hover:bg-primary-600 rounded-md transition-colors">Retry</button></div>'),Yr=T('<p class="text-xs mt-1">All tasks are completed or set as current focus!</p>'),Qr=T('<div class="p-4 rounded-md bg-gray-50 dark:bg-gray-700/30 text-gray-600 dark:text-gray-400 border border-dashed border-gray-300 dark:border-gray-600 mx-2 my-2 text-center text-sm"><p>No tasks in this list.</p> <!></div>'),Gr=(o,e,t)=>e(r(t)),Jr=T('<span class="ml-2 text-xs px-1.5 py-0.5 bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300 rounded-full">High</span>'),Xr=T('<span class="ml-2 text-xs px-1.5 py-0.5 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300 rounded-full">Medium</span>'),Zr=T('<p class="text-xs text-blue-600 dark:text-blue-400 mt-1"> </p>'),Ir=(o,e,t)=>e(r(t)),et=T('<div class="h-4 w-4 border-2 border-current border-t-transparent rounded-full animate-spin"></div>'),rt=ze('<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>'),tt=(o,e,t)=>e(r(t)),ot=(o,e,t)=>e(r(t)),at=T('<li><div class="flex items-center"><input type="checkbox" class="w-4 h-4 mr-3 rounded border-blue-300 text-blue-500 focus:ring-blue-500"> <div class="flex-grow"><div class="flex items-center"><span> </span> <!></div> <!></div> <div class="flex space-x-1"><button><!></button> <button class="p-1 text-gray-400 hover:text-blue-500 transition-colors" title="Edit" aria-label="Edit task"><svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor"><path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"></path></svg></button> <button class="p-1 text-gray-400 hover:text-red-500 transition-colors" title="Delete" aria-label="Delete task"><svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg></button></div></div></li>'),dt=T('<ul class="space-y-2 px-2"></ul>'),st=T('<div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm py-2"><!> <!> <!></div>');function sr(o,e){Te(e,!0);const[t,c]=$e(),y=()=>he(ue,"$todoStore",t);let s=F(!1),u=F(null),_=F(!1);async function g(n){try{await ue.toggleCompleteStatus(n.id,n.status)}catch(j){console.error("Failed to toggle todo status:",j)}}function h(n){i(u,{...n},!0),i(_,!0)}function R(n){console.log("Edit successful for todo:",n.title),i(_,!1),i(u,null)}function C(){i(_,!1),i(u,null)}async function ae(n){if(confirm(`Are you sure you want to delete "${n.title}"?`))try{await ue.removeTodo(n.id)}catch(j){console.error("Failed to delete todo:",j)}}function k(){i(s,!0)}function B(){i(s,!1)}function H(){i(s,!1)}let f=F(Me(new Set));async function O(n){if(n.is_current_focus){if(confirm(`Remove "${n.title}" from Main Focus?`)){r(f).add(n.id),i(f,new Set(r(f)),!0);try{await ue.toggleCurrentFocus(n.id)}finally{r(f).delete(n.id),i(f,new Set(r(f)),!0)}}}else{r(f).add(n.id),i(f,new Set(r(f)),!0);try{await ue.toggleCurrentFocus(n.id)}finally{r(f).delete(n.id),i(f,new Set(r(f)),!0)}}}let L=oe(e,"todos",19,()=>[]),ie=oe(e,"listTitle",3,""),X=oe(e,"addButtonId",3,"add-todo-button");function I(){k()}De(()=>(typeof window<"u"&&(window[`toggleAddForm_${X()}`]=k),()=>{typeof window<"u"&&delete window[`toggleAddForm_${X()}`]}));var w=st(),m=d(w);Vr(m,{get isOpen(){return r(s)},onAddSuccess:B,onCloseRequest:H});var M=l(m,2);{var A=n=>{Kr(n,{get todo(){return r(u)},get isOpen(){return r(_)},onSaveSuccess:R,onCloseRequest:C})};z(M,n=>{r(u)&&n(A)})}var q=l(M,2);{var Q=n=>{var j=Ur();p(n,j)},N=(n,j)=>{{var de=$=>{var ee=Nr(),G=d(ee),se=d(G);a(G);var V=l(G,2);V.__click=()=>ue.loadAllTodos(),a(ee),be(()=>me(se,`Failed to load tasks: ${y().error??""}`)),p($,ee)},ve=($,ee)=>{{var G=V=>{var W=Qr(),ne=l(d(W),2);{var x=J=>{var P=Yr();p(J,P)};z(ne,J=>{ie().toLowerCase().includes("active")&&J(x)})}a(W),p(V,W)},se=V=>{var W=dt();nr(W,21,L,ne=>ne.id,(ne,x)=>{var J=at(),P=d(J),v=d(P);Se(v),v.__change=[Gr,g,x];var D=l(v,2),Y=d(D),ge=d(Y),pe=d(ge,!0);a(ge);var ye=l(ge,2);{var xe=Z=>{var fe=Jr();p(Z,fe)},we=(Z,fe)=>{{var Ne=Ee=>{var gr=Xr();p(Ee,gr)};z(Z,Ee=>{r(x).priority==="medium"&&Ee(Ne)},fe)}};z(ye,Z=>{r(x).priority==="high"?Z(xe):Z(we,!1)})}a(Y);var ke=l(Y,2);{var K=Z=>{var fe=Zr(),Ne=d(fe);a(fe),be(Ee=>me(Ne,`Due: ${Ee??""}`),[()=>new Date(r(x).due_date).toLocaleDateString()]),p(Z,fe)};z(ke,Z=>{r(x).due_date&&Z(K)})}a(D);var b=l(D,2),E=d(b);E.__click=[Ir,O,x];var _e=d(E);{var We=Z=>{var fe=et();p(Z,fe)},Ke=Z=>{var fe=rt();p(Z,fe)};z(_e,Z=>{r(f).has(r(x).id)?Z(We):Z(Ke,!1)})}a(E);var qe=l(E,2);qe.__click=[tt,h,x];var Ue=l(qe,2);Ue.__click=[ot,ae,x],a(b),a(P),a(J),be((Z,fe)=>{U(J,1,`border border-blue-200 dark:border-blue-700 rounded-md p-3 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors ${r(x).is_current_focus?"bg-amber-50 dark:bg-amber-900/20 border-amber-200 dark:border-amber-700":""}`),cr(v,r(x).status==="completed"),U(ge,1,`font-medium text-blue-900 dark:text-blue-100 ${r(x).status==="completed"?"line-through text-blue-500/70 dark:text-blue-400/70":""}`),me(pe,r(x).title),U(E,1,`p-1 ${r(x).is_current_focus?"text-yellow-500 hover:text-yellow-600":"text-gray-400 hover:text-yellow-500"} transition-colors ${Z??""}`),ce(E,"title",r(x).is_current_focus?"Remove from Main Focus":"Set as Main Focus"),ce(E,"aria-label",r(x).is_current_focus?"Remove from Main Focus":"Set as Main Focus"),E.disabled=fe},[()=>r(f).has(r(x).id)?"opacity-50 cursor-not-allowed":"",()=>r(f).has(r(x).id)]),p(ne,J)}),a(W),p(V,W)};z($,V=>{L().length===0?V(G):V(se,!1)},ee)}};z(n,$=>{y().error&&L().length===0&&y().todos.length===0?$(de):$(ve,!1)},j)}};z(q,n=>{y().isLoading&&L().length===0&&y().todos.length===0?n(Q):n(N,!1)})}a(w),p(o,w);var le=Fe({triggerAddForm:I});return c(),le}Ae(["click","change"]);async function it(o,e){return r(e)?await r(e).handleSubmit():!1}var lt=ze('<svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg> 保存中...',1),nt=T('<div class="flex justify-end space-x-3 p-4 border-t border-gray-200 dark:border-gray-700"><button type="button" class="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-400 dark:focus:ring-gray-500 disabled:opacity-70 disabled:cursor-not-allowed transition-colors">取消</button> <button type="button" class="px-4 py-2 bg-amber-600 text-white rounded-md hover:bg-amber-700 focus:outline-none focus:ring-2 focus:ring-amber-400 disabled:opacity-70 disabled:cursor-not-allowed transition-colors flex items-center"><!></button></div>');function ct(o,e){Te(e,!0);let t=oe(e,"isOpen",3,!1),c=oe(e,"isLoading",3,!1),y=oe(e,"onSaveSuccess",3,k=>{}),s=oe(e,"onCloseRequest",3,()=>{}),u=Me(c()),_=F(null);function g(){s()()}De(()=>{document.addEventListener("keydown",h)}),Pe(()=>{document.removeEventListener("keydown",h)});function h(k){k.key==="Escape"&&t()&&g()}var R=Ge(),C=Xe(R);{var ae=k=>{Ie(k,{get isOpen(){return t()},close:g,title:"编辑主要焦点项目",modalWidth:"max-w-xl",children:(B,H)=>{Ve(vr(B,{get todo(){return e.todo},get isLoading(){return u},get onSaveSuccess(){return y()},onCloseModalRequest:g}),f=>i(_,f,!0),()=>r(_))},$$slots:{default:!0,footer:(B,H)=>{var f=nt(),O=d(f);O.__click=g;var L=l(O,2);L.__click=[it,_];var ie=d(L);{var X=w=>{var m=lt();Le(),p(w,m)},I=w=>{var m=Je("保存更改");p(w,m)};z(ie,w=>{u?w(X):w(I,!1)})}a(L),a(f),be(()=>{O.disabled=u,L.disabled=u}),p(B,f)}}})};z(C,k=>{t()&&e.todo&&k(ae)})}p(o,R),Fe()}Ae(["click"]);var ut=T('<div class="p-4 bg-amber-50 dark:bg-amber-900/20 text-amber-700 dark:text-amber-300 border border-amber-200 dark:border-amber-800 rounded-md text-center"><p>Loading focus items...</p></div>'),vt=T('<div class="p-6 bg-amber-50/50 dark:bg-amber-900/10 text-amber-700 dark:text-amber-400 border border-dashed border-amber-300 dark:border-amber-700 rounded-md text-center"><p>No current focus set. <br> You can mark a task as "Current Focus" from the todo list!</p></div>'),gt=(o,e,t)=>e(r(t).id,r(t).status),bt=T('<p class="mt-1 text-sm text-amber-700 dark:text-amber-300"> </p>'),ft=T('<span class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-amber-100 dark:bg-amber-800/40 text-amber-800 dark:text-amber-200 border border-amber-200 dark:border-amber-700"> </span>'),mt=(o,e,t,c)=>{const y=e().todos.find(s=>s.id===r(t).id);y&&c(y)},pt=(o,e,t)=>e(r(t).id),yt=T('<div class="h-5 w-5 border-2 border-current border-t-transparent rounded-full animate-spin"></div>'),xt=ze('<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>'),_t=(o,e,t)=>e(r(t).id,r(t).title),ht=T('<div class="focus-item p-4 border-b border-amber-200 dark:border-amber-700 last:border-b-0"><div class="flex items-start"><div class="flex-shrink-0 mr-3"><input type="checkbox" class="w-5 h-5 rounded border-amber-300 text-amber-500 focus:ring-amber-500"></div> <div class="flex-grow"><div class="flex items-center"><span class="text-yellow-500 mr-2"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg></span> <h3 class="text-lg font-medium text-amber-900 dark:text-amber-100"> </h3></div> <!> <div class="mt-2 flex flex-wrap gap-2"><!> <span> </span></div></div> <div class="flex-shrink-0 flex space-x-2 ml-4"><button class="p-1 text-amber-600 hover:text-amber-800 dark:text-amber-400 dark:hover:text-amber-300 transition-colors" title="Edit" aria-label="Edit task"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"></path></svg></button> <button class="p-1 text-amber-600 hover:text-blue-500 dark:text-amber-400 dark:hover:text-blue-400 transition-colors disabled:opacity-50" title="Remove from Main Focus" aria-label="Remove from Main Focus"><!></button> <button class="p-1 text-amber-600 hover:text-red-500 dark:text-amber-400 dark:hover:text-red-400 transition-colors" title="Delete" aria-label="Delete task"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg></button></div></div></div>'),wt=T('<div class="focus-item-container border border-amber-200 dark:border-amber-700 rounded-md overflow-hidden bg-amber-50/50 dark:bg-amber-900/10"></div>'),kt=T('<div class="p-4 bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 border border-red-200 dark:border-red-800 rounded-md text-center"><p> </p></div>'),St=T('<div class="focus-display-container"><!> <!> <!></div>');function Tt(o,e){Te(e,!0);const[t,c]=$e(),y=()=>he(Dr,"$currentFocusTodos",t),s=()=>he(ue,"$todoStore",t);let u=F(!1),_=F(null),g=F(Me(new Set)),h=Ye(y);async function R(m){if(confirm("Remove this item from Main Focus?")){r(g).add(m),i(g,new Set(r(g)),!0);try{await ue.toggleCurrentFocus(m)}catch(M){console.error("Failed to remove from focus:",M)}finally{r(g).delete(m),i(g,new Set(r(g)),!0)}}}async function C(m,M){try{await ue.toggleCompleteStatus(m,M)}catch(A){console.error("Failed to toggle complete status:",A)}}async function ae(m,M){if(confirm(`Are you sure you want to delete "${M}"?`))try{await ue.removeTodo(m)}catch(A){console.error("Failed to delete todo:",A)}}function k(m){i(_,m,!0),i(u,!0)}function B(){i(u,!1),i(_,null)}var H=St(),f=d(H);{var O=m=>{var M=ut();p(m,M)},L=(m,M)=>{{var A=Q=>{var N=vt();p(Q,N)},q=Q=>{var N=wt();nr(N,21,()=>r(h),le=>le.id,(le,n)=>{var j=ht(),de=d(j),ve=d(de),$=d(ve);Se($),$.__change=[gt,C,n],a(ve);var ee=l(ve,2),G=d(ee),se=l(d(G),2),V=d(se,!0);a(se),a(G);var W=l(G,2);{var ne=K=>{var b=bt(),E=d(b,!0);a(b),be(()=>me(E,r(n).description)),p(K,b)};z(W,K=>{r(n).description&&K(ne)})}var x=l(W,2),J=d(x);{var P=K=>{var b=ft(),E=d(b);a(b),be(_e=>me(E,`Due: ${_e??""}`),[()=>new Date(r(n).due_date).toLocaleDateString()]),p(K,b)};z(J,K=>{r(n).due_date&&K(P)})}var v=l(J,2),D=d(v);a(v),a(x),a(ee);var Y=l(ee,2),ge=d(Y);ge.__click=[mt,s,n,k];var pe=l(ge,2);pe.__click=[pt,R,n];var ye=d(pe);{var xe=K=>{var b=yt();p(K,b)},we=K=>{var b=xt();p(K,b)};z(ye,K=>{r(g).has(r(n).id)?K(xe):K(we,!1)})}a(pe);var ke=l(pe,2);ke.__click=[_t,ae,n],a(Y),a(de),a(j),be((K,b)=>{ce($,"id",`focus-status-${r(n).id??""}`),cr($,r(n).status==="completed"),me(V,r(n).title),U(v,1,`inline-flex items-center px-2 py-1 rounded-md text-xs font-medium
                  ${r(n).priority==="high"?"bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300 border border-red-200 dark:border-red-800":r(n).priority==="medium"?"bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300 border border-yellow-200 dark:border-yellow-800":"bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 border border-green-200 dark:border-green-800"}`),me(D,`${K??""} Priority`),pe.disabled=b},[()=>r(n).priority.charAt(0).toUpperCase()+r(n).priority.slice(1),()=>r(g).has(r(n).id)]),p(le,j)}),a(N),p(Q,N)};z(m,Q=>{r(h).length===0?Q(A):Q(q,!1)},M)}};z(f,m=>{s().isLoading&&s().todos.length===0?m(O):m(L,!1)})}var ie=l(f,2);{var X=m=>{var M=kt(),A=d(M),q=d(A);a(A),a(M),be(()=>me(q,`Failed to load items: ${s().error??""}`)),p(m,M)};z(ie,m=>{s().error&&s().todos.length===0&&!s().isLoading&&m(X)})}var I=l(ie,2);{var w=m=>{ct(m,{get todo(){return r(_)},get isOpen(){return r(u)},onSaveSuccess:B,onCloseRequest:B})};z(I,m=>{r(_)&&m(w)})}a(H),p(o,H),Fe(),c()}Ae(["change","click"]);function Ft(o,e){r(e)&&r(e).triggerAddForm()}var Ct=(o,e)=>i(e,!r(e)),Lt=T('<div><div class="flex-grow overflow-hidden"><div><div><div><div class="p-4 border-b border-gray-200 dark:border-gray-700"><div class="flex justify-between items-center"><h2><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"><path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"></path><path fill-rule="evenodd" d="M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v11a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z" clip-rule="evenodd"></path></svg> Todo</h2> <button id="add-todo-button" aria-label="Add new todo"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"></path></svg></button></div></div> <div><div></div> <div><!></div></div> <div><button><h3> </h3> <span> </span></button></div></div></div> <div><div><div class="p-6 border-b border-gray-200 dark:border-gray-700"><h1><span>⭐</span> Main Focus:</h1></div> <div class="p-6 flex-grow overflow-auto"><!></div></div></div></div></div></div>');function Ut(o,e){Te(e,!0);const[t,c]=$e(),y=()=>he(Er,"$otherActiveTodos",t),s=()=>he(zr,"$completedTodos",t),u=()=>he(ue,"$todoStore",t),_=()=>he(er,"$authStore",t),g=xr.doing;let h,R=F(!1),C=F(void 0),ae=Ye(y),k=Ye(s);De(async()=>{if(h=er.subscribe(async v=>{if(v.accessToken&&u().todos.length===0&&!u().isLoading&&!u().error)try{await ue.loadAllTodos()}catch(D){console.error("Failed to load todos on auth state change:",D)}}),_().accessToken&&u().todos.length===0&&!u().isLoading&&!u().error)try{await ue.loadAllTodos()}catch(v){console.error("Failed to load todos on mount:",v)}}),Pe(()=>{h&&h()});var B=Lt(),H=d(B),f=d(H),O=d(f),L=d(O),ie=d(L),X=d(ie),I=d(X),w=d(I);Le(),a(I);var m=l(I,2);m.__click=[Ft,C],a(X),a(ie);var M=l(ie,2),A=d(M),q=l(A,2),Q=d(q);{var N=P=>{Ve(sr(P,{get todos(){return r(ae)},addButtonId:"add-todo-button"}),v=>i(C,v,!0),()=>r(C))},le=P=>{Ve(sr(P,{get todos(){return r(k)},addButtonId:"add-todo-button"}),v=>i(C,v,!0),()=>r(C))};z(Q,P=>{r(R)?P(le,!1):P(N)})}a(q),a(M);var n=l(M,2),j=d(n);j.__click=[Ct,R];var de=d(j),ve=d(de,!0);a(de);var $=l(de,2),ee=d($,!0);a($),a(j),a(n),a(L),a(O);var G=l(O,2),se=d(G),V=d(se),W=d(V),ne=d(W);Le(),a(W),a(V);var x=l(V,2),J=d(x);Tt(J,{}),a(x),a(se),a(G),a(f),a(H),a(B),be((P,v,D,Y,ge,pe,ye,xe,we,ke,K,b,E,_e,We,Ke,qe,Ue)=>{U(B,1,P),U(f,1,v),U(O,1,D),U(L,1,Y),U(I,1,ge),U(w,0,pe),U(m,1,ye),U(M,1,xe),U(A,1,we),U(q,1,ke),U(n,1,K),U(j,1,b),U(de,1,E),me(ve,r(R)?"ACTIVE":"PAST"),U($,1,_e),me(ee,r(R)?"Show active tasks":"Show completed tasks"),U(G,1,We),U(se,1,Ke),U(W,1,qe),U(ne,1,Ue)},[()=>re(te(pr,"h-[calc(100vh-180px)] flex flex-col")),()=>re(te(yr.twoColumnOneThree,"h-full")),()=>re(te(rr.oneFourth,"h-full flex flex-col")),()=>re(te(tr,g.border,"h-full flex flex-col")),()=>re(te(or.h2,g.text)),()=>re(te("h-5 w-5 mr-2 inline",g.icon)),()=>re(te("p-1 text-sm rounded-md focus:outline-none focus:ring-2",g.text,g.hover)),()=>re(te(ar.container,"flex-grow relative")),()=>re(te(ar.indicator,"left-0",g.scrollbar)),()=>re(te("pl-3","absolute inset-0 overflow-y-auto pr-2")),()=>re(te("p-4 border-t",g.border)),()=>re(te("flex items-center justify-between w-full text-left p-2 rounded-md transition-colors",g.hover)),()=>re(te("text-lg font-medium",g.text)),()=>re(te("text-sm",g.text)),()=>re(te(rr.threeFourths,"h-full flex flex-col")),()=>re(te(tr,g.border,"h-full flex flex-col")),()=>re(te(or.h1,g.text)),()=>re(te(g.icon,"mr-2"))]),p(o,B),Fe(),c()}Ae(["click"]);export{Ut as component};
