import{t as f,a as v}from"../chunks/CEORzCZH.js";import"../chunks/Dj8TIuzp.js";import{p as B,t as A,i as a,a as C,s as n,c as t,r as o,w as p,n as F,x as i}from"../chunks/B9CKrN7X.js";import{e as G,s as H}from"../chunks/Cc0s-Eqn.js";import{i as P}from"../chunks/C9zVtFY0.js";import{r as k}from"../chunks/DUXSSeDn.js";import{b as q}from"../chunks/Bk_lFxuP.js";import{p as I}from"../chunks/CWmzcjye.js";import{i as J}from"../chunks/ZU85TcGt.js";import{o as K}from"../chunks/D6jnoBFN.js";import{g as N}from"../chunks/zBAGAr4e.js";import{a as O}from"../chunks/DNwzTOhB.js";import{i as Q}from"../chunks/CaTJm4T8.js";var R=f('<div class="error-message svelte-1ds17bf"><p class="svelte-1ds17bf"> </p></div>'),T=f('<span class="svelte-1ds17bf">Loading...</span>'),V=f('<span class="svelte-1ds17bf">Login</span>'),X=f(`<div class="login-container svelte-1ds17bf"><div class="login-card svelte-1ds17bf"><h1 class="card-title svelte-1ds17bf">Login</h1> <p class="card-subtitle svelte-1ds17bf">Welcome back! Please enter your credentials.</p> <form class="login-form svelte-1ds17bf"><div class="form-group svelte-1ds17bf"><label for="email" class="svelte-1ds17bf">Email Address</label> <input type="email" id="email" required placeholder="<EMAIL>" class="svelte-1ds17bf"></div> <div class="form-group svelte-1ds17bf"><label for="password" class="svelte-1ds17bf">Password</label> <input type="password" id="password" required placeholder="••••••••" class="svelte-1ds17bf"></div> <!> <button type="submit" class="submit-button svelte-1ds17bf"><!></button></form> <div class="alternative-action svelte-1ds17bf"><p class="svelte-1ds17bf">Don't have an account? <a href="/register" class="svelte-1ds17bf">Sign up here</a></p></div></div></div>`);function vs(D,E){B(E,!1);let c=p(""),m=p(""),l=p(""),d=p(!1);K(()=>Q.subscribe(e=>{e&&N("/doing")}));async function M(){i(d,!0),i(l,"");const s={email:a(c),password:a(m)};try{await O.loginUser(s)}catch(e){const r=e;r.data&&r.data.message?i(l,r.data.message):r.message?i(l,r.message):i(l,"An unexpected error occurred. Please try again."),console.error("Login page error:",r)}finally{i(d,!1)}}J();var b=X(),x=t(b),u=n(t(x),4),g=t(u),_=n(t(g),2);k(_),o(g);var h=n(g,2),y=n(t(h),2);k(y),o(h);var L=n(h,2);{var S=s=>{var e=R(),r=t(e),z=t(r,!0);o(r),o(e),A(()=>H(z,a(l))),v(s,e)};P(L,s=>{a(l)&&s(S)})}var w=n(L,2),U=t(w);{var W=s=>{var e=T();v(s,e)},j=s=>{var e=V();v(s,e)};P(U,s=>{a(d)?s(W):s(j,!1)})}o(w),o(u),F(2),o(x),o(b),A(()=>{_.disabled=a(d),y.disabled=a(d),w.disabled=a(d)}),q(_,()=>a(c),s=>i(c,s)),q(y,()=>a(m),s=>i(m,s)),G("submit",u,I(M)),v(D,b),C()}export{vs as component};
