import{t as h,a as l}from"../chunks/CEORzCZH.js";import"../chunks/Dj8TIuzp.js";import{p as v,f as u,t as g,a as x,c as e,r as p,s as _}from"../chunks/B9CKrN7X.js";import{s as o}from"../chunks/Cc0s-Eqn.js";import{i as d}from"../chunks/ZU85TcGt.js";import{p as m}from"../chunks/7w2adJpR.js";var b=h("<h1> </h1> <p> </p>",1);function z(i,f){v(f,!1),d();var r=b(),t=u(r),n=e(t,!0);p(t);var a=_(t,2),c=e(a,!0);p(a),g(()=>{var s;o(n,m.status),o(c,(s=m.error)==null?void 0:s.message)}),l(i,r),x()}export{z as component};
