import{t as k,a as f,n as me}from"../chunks/CEORzCZH.js";import"../chunks/Dj8TIuzp.js";import{p as J,c as l,r as n,t as B,i as e,Q as $e,a as Z,x as a,P as D,d as de,s as x,f as te,n as Pe,_ as Ne,aK as he,aL as Te,w as ue,X as De}from"../chunks/B9CKrN7X.js";import{i as C}from"../chunks/C9zVtFY0.js";import{e as Me,i as ze,s as Ae}from"../chunks/D8moldTO.js";import{i as Se}from"../chunks/ZU85TcGt.js";import{o as oe,g as je}from"../chunks/D6jnoBFN.js";/* empty css                */import{g as re}from"../chunks/DgxGL_Xb.js";import{p as T,n as se}from"../chunks/7w2adJpR.js";import{a as be,i as le}from"../chunks/CaTJm4T8.js";import{d as ce,s as Q}from"../chunks/Cc0s-Eqn.js";import{a as xe,s as Y,c as ne}from"../chunks/DUXSSeDn.js";import{s as pe}from"../chunks/BIuBsydj.js";import{p as W,r as ke}from"../chunks/B16-Q6Ob.js";import{a as Le}from"../chunks/CjIxvcxe.js";import{B as Ce}from"../chunks/hBTlxZOa.js";import{s as Ee,a as Ie}from"../chunks/BIYeehul.js";import{i as Ve,t as We}from"../chunks/BCVqPHae.js";var Be=me('<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2" class="w-full h-full"><path stroke-linecap="round" stroke-linejoin="round" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path></svg>'),Fe=me('<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2" class="w-full h-full"><path stroke-linecap="round" stroke-linejoin="round" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path></svg>'),Re=k("<button><!></button>");function Ge(m,i){J(i,!0);const[o,b]=Ie(),d=()=>Ee(Ve,"$isDarkMode",o);let p=W(i,"size",3,"md"),c=W(i,"class",3,""),w=ke(i,["$$slots","$$events","$$legacy","size","class"]);const _={sm:"w-8 h-8",md:"w-10 h-10",lg:"w-12 h-12"},h=$e(()=>_[p()]);function g(){We.toggleDarkMode()}var u=Re();let r;var t=l(u);{var v=$=>{var P=Be();f($,P)},M=$=>{var P=Fe();f($,P)};C(t,$=>{d()?$(v):$(M,!1)})}n(u),B(()=>r=xe(u,r,{type:"button",class:`rounded-full p-2 transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 ${d()?"bg-gray-800 text-yellow-300 hover:bg-gray-700":"bg-gray-200 text-gray-900 hover:bg-gray-300"} ${e(h)} ${c()}`,"aria-label":d()?"Switch to light mode":"Switch to dark mode",title:d()?"Switch to light mode":"Switch to dark mode",onclick:g,...w})),f(m,u),Z(),b()}function Ue(m,i){a(i,!e(i))}async function He(){try{console.log("Navbar: Starting logout process"),await Le.logoutUser(),console.log("Navbar: Logout successful");try{await re("/login")}catch(m){console.error("Navbar: Error redirecting after logout:",m),window.location.href="/login"}}catch(m){console.error("Navbar: Failed to logout:",m),je(le)&&(console.log("Navbar: Forcing client-side logout"),be.logout());try{await re("/login")}catch(i){console.error("Navbar: Error redirecting after failed logout:",i),window.location.href="/login"}}}var Oe=k('<div class="px-6 py-2 min-w-[180px] text-center text-3xl font-extrabold text-indigo-900 dark:text-indigo-100 tracking-wide relative group svelte-kghh9c"><span class="relative z-10 svelte-kghh9c">My Anchor</span> <span class="absolute inset-0 bg-indigo-200 dark:bg-indigo-800 rounded-lg transform scale-95 opacity-20 group-hover:scale-100 group-hover:opacity-30 transition-all duration-300 svelte-kghh9c"></span></div>'),Xe=(m,i,o)=>i(e(o).path),qe=k('<button type="button"><div class="w-32 h-24 mb-2 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center text-4xl shadow-inner overflow-hidden relative svelte-kghh9c"><div class="absolute inset-0 bg-gradient-to-br from-transparent to-gray-200 dark:to-gray-600 opacity-50 svelte-kghh9c"></div> <span class="relative z-10 transform transition-transform duration-300 group-hover:scale-110 svelte-kghh9c"> </span></div> <div class="font-bold text-indigo-900 dark:text-indigo-100 svelte-kghh9c"> </div> <div class="text-xs text-gray-600 dark:text-gray-400 text-center mt-1 max-w-[150px] svelte-kghh9c"> </div></button>'),Ke=k('<div class="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 w-[600px] bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700 z-50 overflow-hidden animate-dropdown svelte-kghh9c"><div class="flex p-4 justify-around svelte-kghh9c"></div></div>'),Qe=k('<button type="button" class="px-6 py-2 min-w-[180px] text-center text-3xl font-extrabold text-indigo-900 dark:text-indigo-100 tracking-wide relative group cursor-pointer bg-transparent border-0 svelte-kghh9c" aria-haspopup="true"><span class="relative z-10 flex items-center justify-center svelte-kghh9c"> <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" class="svelte-kghh9c"></path></svg></span> <span class="absolute inset-0 bg-indigo-200 dark:bg-indigo-800 rounded-lg transform scale-95 opacity-20 group-hover:scale-100 group-hover:opacity-30 transition-all duration-300 svelte-kghh9c"></span></button> <!>',1),Ye=k('<div class="flex items-center svelte-kghh9c"><span class="mr-3 text-base font-medium text-indigo-700 dark:text-indigo-300 svelte-kghh9c"> </span> <button class="inline-flex items-center px-3 py-2 rounded-md bg-indigo-100 dark:bg-indigo-800 border border-indigo-300 dark:border-indigo-600 text-indigo-700 dark:text-indigo-300 hover:bg-indigo-200 dark:hover:bg-indigo-700 transition-colors svelte-kghh9c" title="Logout" aria-label="Logout"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2 svelte-kghh9c"><path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4" class="svelte-kghh9c"></path><polyline points="16 17 21 12 16 7" class="svelte-kghh9c"></polyline><line x1="21" y1="12" x2="9" y2="12" class="svelte-kghh9c"></line></svg> <span class="svelte-kghh9c">Logout</span></button></div>'),Je=k('<div class="flex items-center space-x-2 svelte-kghh9c"><a href="/login" class="inline-flex items-center px-3 py-2 rounded-md bg-indigo-100 dark:bg-indigo-800 text-indigo-700 dark:text-indigo-300 hover:bg-indigo-200 dark:hover:bg-indigo-700 transition-colors svelte-kghh9c">Login</a> <a href="/register" class="inline-flex items-center px-3 py-2 rounded-md bg-indigo-100 dark:bg-indigo-800 text-indigo-700 dark:text-indigo-300 hover:bg-indigo-200 dark:hover:bg-indigo-700 transition-colors svelte-kghh9c">Register</a></div>'),Ze=k('<nav class="sticky top-0 z-navbar bg-indigo-50 dark:bg-indigo-900 border-b border-indigo-200 dark:border-indigo-700 shadow-sm svelte-kghh9c"><div class="flex justify-between items-center w-full px-4 py-3 svelte-kghh9c"><div class="flex-shrink-0 svelte-kghh9c"><a href="/doing" class="flex items-center text-2xl font-bold text-indigo-900 dark:text-indigo-100 hover:opacity-85 transition-opacity svelte-kghh9c" aria-label="Go to Dashboard">Personal Workspace</a></div> <div class="flex-grow flex justify-center items-center svelte-kghh9c"><div class="page-dropdown-container relative svelte-kghh9c"><!></div></div> <div class="flex items-center svelte-kghh9c"><div class="mr-3 svelte-kghh9c"><!></div> <div class="mr-4 svelte-kghh9c"><!></div> <!></div></div></nav>');function fe(m,i){J(i,!0);let o=W(i,"isAnchorPage",3,!1),b=W(i,"currentViewDisplay",3,"N/A"),d=D(null),p=D("/doing"),c=D(!1);const w=[{path:"/done",display:"Done",icon:"✓",description:"View your completed tasks and achievements"},{path:"/doing",display:"Doing",icon:"⚡",description:"Manage your current tasks and focus"},{path:"/plan",display:"Plan",icon:"📝",description:"Plan your future tasks and projects"}];function _(s){const y=s.target;e(c)&&!y.closest(".page-dropdown-container")&&a(c,!1)}function h(s){a(c,!1),window.location.href=s}be.subscribe(s=>{a(d,s.user,!0)}),oe(()=>(document.addEventListener("click",_),()=>{document.removeEventListener("click",_)})),de(()=>{try{if(o()){const s=sessionStorage.getItem("lastWorkspacePage");s?(a(p,s,!0),console.log("Retrieved lastWorkspacePage:",e(p))):(a(p,"/doing"),console.log("No stored lastWorkspacePage, using default:",e(p)))}else{const s=window.location.pathname;s!=="/anchor"&&(s==="/doing"||s==="/done"||s==="/plan"?(a(p,s,!0),sessionStorage.setItem("lastWorkspacePage",e(p)),console.log("Stored lastWorkspacePage:",e(p))):a(p,"/doing"))}}catch(s){console.error("Error updating lastWorkspacePage:",s),a(p,"/doing")}});var g=Ze(),u=l(g),r=x(l(u),2),t=l(r),v=l(t);{var M=s=>{var y=Oe();f(s,y)},$=s=>{var y=Qe(),z=te(y);z.__click=[Ue,c];var G=l(z),U=l(G),N=x(U);n(G),Pe(2),n(z);var j=x(z,2);{var I=A=>{var L=Ke(),V=l(L);Me(V,21,()=>w,ze,(K,H)=>{var O=qe();O.__click=[Xe,h,H];var ae=l(O),ge=x(l(ae),2),ye=l(ge,!0);n(ge),n(ae);var ie=x(ae,2),we=l(ie,!0);n(ie);var ve=x(ie,2),_e=l(ve,!0);n(ve),n(O),B(()=>{pe(O,1,`flex flex-col items-center p-3 rounded-lg hover:bg-indigo-50 dark:hover:bg-indigo-900/30 transition-colors cursor-pointer ${e(H).path===window.location.pathname?"bg-indigo-100 dark:bg-indigo-800/50":""} bg-transparent border-0 w-full`,"svelte-kghh9c"),Y(O,"aria-label",`Navigate to ${e(H).display??""} page`),Q(ye,e(H).icon),Q(we,e(H).display),Q(_e,e(H).description)}),f(K,O)}),n(V),n(L),f(A,L)};C(j,A=>{e(c)&&A(I)})}B(()=>{Y(z,"aria-expanded",e(c)),Q(U,`${b()??""} `),pe(N,0,`h-5 w-5 ml-2 transform transition-transform duration-300 ${e(c)?"rotate-180":""}`,"svelte-kghh9c")}),f(s,y)};C(v,s=>{o()?s(M):s($,!1)})}n(t),n(r);var P=x(r,2),S=l(P),F=l(S);Ge(F,{size:"sm"}),n(S);var E=x(S,2),X=l(E);Ce(X,{}),n(E);var R=x(E,2);{var ee=s=>{var y=Ye(),z=l(y),G=l(z);n(z);var U=x(z,2);U.__click=[He],n(y),B(N=>{Y(z,"title",e(d).email||""),Q(G,`Hello, ${N??""}!`)},[()=>{var N;return e(d).username||((N=e(d).email)==null?void 0:N.split("@")[0])}]),f(s,y)},q=s=>{var y=Je();f(s,y)};C(R,s=>{e(d)?s(ee):s(q,!1)})}n(P),n(u),n(g),f(m,g),Z()}ce(["click"]);var et=(m,i)=>i("prev"),tt=k('<div class="fixed left-0 top-1/2 transform -translate-y-1/2 z-40 svelte-y0pwd6"><button class="arrow-button arrow-left inline-flex items-center justify-center p-5 bg-gray-500/30 hover:bg-gray-600/70 active:bg-gray-700/80 dark:bg-gray-700/30 dark:hover:bg-gray-600/70 dark:active:bg-gray-500/80 text-gray-700 hover:text-white dark:text-gray-300 dark:hover:text-white font-medium rounded-md border border-gray-400/30 dark:border-gray-500/30 shadow-lg h-[calc(100vh/3)] aspect-[2/5] svelte-y0pwd6" aria-label="Go to previous section"><svg xmlns="http://www.w3.org/2000/svg" class="w-1/1 h-1/1 svelte-y0pwd6" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"><polyline points="15 18 9 12 15 6" class="svelte-y0pwd6"></polyline></svg></button></div>'),rt=(m,i)=>i("next"),ot=k('<div class="fixed right-0 top-1/2 transform -translate-y-1/2 z-40 svelte-y0pwd6"><button class="arrow-button arrow-right inline-flex items-center justify-center p-5 bg-gray-500/30 hover:bg-gray-600/70 active:bg-gray-700/80 dark:bg-gray-700/30 dark:hover:bg-gray-600/70 dark:active:bg-gray-500/80 text-gray-700 hover:text-white dark:text-gray-300 dark:hover:text-white font-medium rounded-md border border-gray-400/30 dark:border-gray-500/30 shadow-lg h-[calc(100vh/3)] aspect-[2/5] svelte-y0pwd6" aria-label="Go to next section"><svg xmlns="http://www.w3.org/2000/svg" class="w-1/1 h-1/1 svelte-y0pwd6" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"><polyline points="9 18 15 12 9 6" class="svelte-y0pwd6"></polyline></svg></button></div>'),at=k("<!> <!>",1);function it(m,i){J(i,!0);const o=[{path:"done",display:"Done"},{path:"doing",display:"Doing"},{path:"plan",display:"Plan"}];let b=W(i,"currentViewDisplay",15,"N/A"),d=D(-1);function p(r){console.log("Current path:",r);for(let t=0;t<o.length;t++)if(r===`/${o[t].path}`){a(d,t,!0),b(o[t].display),console.log(`Exact match found: ${o[t].path}, index: ${t}`);return}for(let t=0;t<o.length;t++)if(r.includes(`/${o[t].path}`)){a(d,t,!0),b(o[t].display),console.log(`Partial match found: ${o[t].path}, index: ${t}`);return}a(d,-1),b("N/A"),console.log("No match found")}oe(()=>{p(T.url.pathname)}),de(()=>{p(T.url.pathname)});function c(r){try{let t;if(b()==="Done"&&r==="prev"){console.log("Already at the first page (Done), cannot go previous");return}if(b()==="Plan"&&r==="next"){console.log("Already at the last page (Plan), cannot go next");return}e(d)===-1?t=r==="next"?0:o.length-1:r==="prev"?t=(e(d)-1+o.length)%o.length:t=(e(d)+1)%o.length;const v=`/${o[t].path}`;console.log(`Navigating to: ${v}`);try{window.location.href=v}catch(M){console.error("Navigation error:",M),window.history.pushState({},"",v),window.dispatchEvent(new Event("popstate"))}}catch(t){console.error("Error in navigateTo function:",t),window.location.href=r==="next"?"/doing":"/done"}}var w=at(),_=te(w);{var h=r=>{var t=tt(),v=l(t);v.__click=[et,c],n(t),B(()=>Y(v,"title",`Previous: ${(e(d)!==-1&&e(d)>0?o[(e(d)-1+o.length)%o.length].display:o[o.length-1].display)??""}`)),f(r,t)};C(_,r=>{b()!=="Done"&&r(h)})}var g=x(_,2);{var u=r=>{var t=ot(),v=l(t);v.__click=[rt,c],n(t),B(()=>Y(v,"title",`Next: ${(e(d)!==-1?o[(e(d)+1)%o.length].display:o[0].display)??""}`)),f(r,t)};C(g,r=>{b()!=="Plan"&&r(u)})}f(m,w),Z()}ce(["click"]);var st=()=>window.location.reload(),nt=(m,i)=>{a(i,!1)},lt=k('<div class="fixed inset-0 flex items-center justify-center bg-white/80 dark:bg-gray-900/80 z-50"><div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg max-w-md text-center"><h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Loading seems to be stuck</h3> <p class="text-gray-600 dark:text-gray-300 mb-6">The page is taking longer than expected to load. This might be due to a network issue or a problem with the application.</p> <div class="flex justify-center space-x-4"><button class="px-4 py-2 bg-primary-500 hover:bg-primary-600 text-white rounded-md transition-colors">Reload Page</button> <button class="px-4 py-2 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-800 dark:text-white rounded-md transition-colors">Continue Waiting</button></div></div></div>'),dt=k('<div><div class="relative h-full"><div class="absolute top-0 left-0 right-0 bg-gray-200 dark:bg-gray-700 opacity-30"></div> <div class="h-full bg-current relative"><div class="absolute right-0 top-0 bottom-0 w-4 bg-white opacity-50 animate-pulse"></div></div></div> <!></div>');function ct(m,i){J(i,!0);let o=W(i,"height",3,"3px"),b=W(i,"color",3,"var(--color-primary-500, #6366f1)"),d=W(i,"class",3,""),p=ke(i,["$$slots","$$events","$$legacy","height","color","class"]),c=D(0),w=D(!1),_=D(!1),h=null,g=null,u=D(!1),r=D(null),t=D(0),v=D(Ne([])),M=D(0),$=D(0);const P=5,S=15;de(()=>{var A,L;const N=se.from!==null,j=(A=se.from)==null?void 0:A.url.pathname,I=(L=se.to)==null?void 0:L.url.pathname;if(N)a(r,performance.now(),!0),e(v).length>0?(a(M,e(v).reduce((V,K)=>V+K,0)/e(v).length),a($,e(r)+e(M))):a($,e(r)+500),console.log(`Navigation started: ${j} -> ${I}`),console.log(`Expected completion in ~${e(M).toFixed(0)}ms based on history`),F();else if(e(w)){if(e(r)){const V=performance.now();a(t,V-e(r)),a(v,[e(t),...e(v).slice(0,P-1)],!0),console.log(`Navigation completed in ${e(t).toFixed(0)}ms`),console.log(`Navigation history: ${e(v).map(K=>K.toFixed(0)).join(", ")}ms`),a(r,null)}X()}});function F(){a(w,!0),a(_,!1),a(c,S),a(u,!1),h&&(clearTimeout(h),h=null),g&&(clearTimeout(g),g=null),g=setTimeout(()=>{e(w)&&!e(_)&&(a(u,!0),console.warn("Loading seems to be taking longer than expected"))},8e3),E()}function E(){if(!(!e(w)||e(_))&&e(c)<90){if(e(r)&&e($)){const j=performance.now()-e(r),I=e($)-e(r),A=Math.min(90,S+j/I*(90-S));a(c,e(c)+(A-e(c))*.2)}else{const N=(100-e(c))/10;a(c,e(c)+Math.min(N,5))}h=setTimeout(E,50)}}function X(){a(c,100),a(_,!0),a(u,!1),g&&(clearTimeout(g),g=null),h=setTimeout(()=>{a(w,!1),a(c,0)},300)}oe(()=>()=>{h&&clearTimeout(h),g&&clearTimeout(g)});var R=dt();let ee;var q=l(R),s=l(q),y=x(s,2),z=l(y);n(y),n(q);var G=x(q,2);{var U=N=>{var j=lt(),I=l(j),A=x(l(I),4),L=l(A);L.__click=[st];var V=x(L,2);V.__click=[nt,u],n(A),n(I),n(j),f(N,j)};C(G,N=>{e(u)&&N(U)})}n(R),B(()=>{ee=xe(R,ee,{class:`fixed top-0 left-0 right-0 z-50 ${e(u)?"pointer-events-auto":"pointer-events-none"} ${d()} ${e(w)?"opacity-100":"opacity-0"}`,style:"transition: opacity 300ms ease-in-out;",...p}),ne(s,`height: ${o()};`),ne(y,`
        height: ${o()};
        width: ${e(c)}%;
        transition: width 300ms ease-out, opacity 300ms ease-in-out;
        background-color: ${b()};
        ${e(_)?"transition-timing-function: cubic-bezier(0.19, 1, 0.22, 1);":""}
      `),ne(z,`height: ${o()};`)}),f(m,R),Z()}ce(["click"]);var gt=k("<!> <!>",1),vt=k('<div class="flex flex-col min-h-screen dark:bg-transparent"><!> <main class="flex-grow flex flex-col p-4"><div><!></div></main></div>'),ht=k('<div class="flex justify-center items-center min-h-screen text-2xl text-secondary-500 dark:text-secondary-400"><div class="flex flex-col items-center"><div class="w-16 h-16 border-4 border-primary-500 border-t-transparent rounded-full animate-spin mb-4"></div> <p>Loading...</p></div></div>'),ut=k("<!> <!>",1);function Lt(m,i){J(i,!1);let o=ue(!1),b=ue();oe(()=>{const h=le.subscribe(r=>{r?a(o,!0):T.route.id!=="/login"&&T.route.id!=="/register"&&re("/login")});let g=!1;return le.subscribe(r=>g=r)(),g?a(o,!0):T.route.id!=="/login"&&T.route.id!=="/register"&&re("/login"),()=>{h()}}),he(()=>T,()=>{a(b,T.url.pathname.startsWith("/anchor"))}),he(()=>T,()=>{T.url.pathname}),Te(),Se();var d=ut(),p=te(d);ct(p,{});var c=x(p,2);{var w=h=>{var g=vt(),u=l(g);{var r=P=>{var S=gt(),F=te(S);it(F,{});var E=x(F,2);const X=De(()=>T.url.pathname==="/done"?"Done":T.url.pathname==="/doing"?"Doing":T.url.pathname==="/plan"?"Plan":"N/A");fe(E,{get isAnchorPage(){return e(b)},get currentViewDisplay(){return e(X)}}),f(P,S)},t=P=>{fe(P,{get isAnchorPage(){return e(b)}})};C(u,P=>{e(b)?P(t,!1):P(r)})}var v=x(u,2),M=l(v),$=l(M);Ae($,i,"default",{},null),n(M),n(v),n(g),f(h,g)},_=h=>{var g=ht();f(h,g)};C(c,h=>{e(o)?h(w):h(_,!1)})}f(m,d),Z()}export{Lt as component};
