import{t as k,a as h,n as be,d as Je}from"../chunks/CEORzCZH.js";import{p as ze,c as r,r as t,s as n,P as U,i as a,t as S,a as Ee,x as l,_ as ye,d as ot,Q as Ye,n as He}from"../chunks/B9CKrN7X.js";import{d as Be,s as A,r as vt,e as ct}from"../chunks/Cc0s-Eqn.js";import{i as M}from"../chunks/C9zVtFY0.js";import{e as Ze,i as $e}from"../chunks/D8moldTO.js";import{c as B,s as j}from"../chunks/BIuBsydj.js";import{p as et}from"../chunks/B16-Q6Ob.js";import{a as tt,s as Se}from"../chunks/BIYeehul.js";import{g as De,w as Ie,o as ut}from"../chunks/D6jnoBFN.js";import{b as ke}from"../chunks/DNwzTOhB.js";import{a as rt}from"../chunks/CaTJm4T8.js";import{s as Ne,r as Le}from"../chunks/DUXSSeDn.js";import{b as Ce}from"../chunks/Bk_lFxuP.js";import{c as T,p as ht,l as mt,a as Ke,b as gt,d as We,h as Xe,s as Ge,M as ft}from"../chunks/CtrCr3Rb.js";const Ae="/achievements/",je={async getAchievements(){try{return await ke.get(Ae)}catch(d){throw console.error("Error fetching achievements:",d),d}},async createAchievement(d){try{return await ke.post(Ae,d)}catch(e){throw console.error("Error creating achievement:",e),e}},async getAchievementById(d){try{return await ke.get(`${Ae}/${d}`)}catch(e){throw console.error(`Error fetching achievement with ID ${d}:`,e),e}},async updateAchievement(d,e){try{return await ke.put(`${Ae}${d}`,e)}catch(i){throw console.error(`Error updating achievement with ID ${d}:`,i),i}},async deleteAchievement(d){try{await ke.delete(`${Ae}${d}`)}catch(e){throw console.error(`Error deleting achievement with ID ${d}:`,e),e}}},xt=()=>{const d=Ie([]),e=Ie(!1),i=Ie(null);let x=null;return rt.subscribe(c=>{x=c.user?c.user.id:null}),{achievements:d,isLoading:e,error:i,loadAchievements:async()=>{if(!x){i.set("用户未登录，无法加载成就。"),d.set([]);return}e.set(!0),i.set(null);try{const c=await je.getAchievements();d.set(c||[])}catch(c){i.set(c.message||"加载成就失败。"),d.set([])}finally{e.set(!1)}},addAchievement:async c=>{if(!x)return i.set("用户未登录，无法添加成就。"),null;e.set(!0),i.set(null);try{const g=await je.createAchievement(c);return g&&d.update(m=>[g,...m].sort((p,N)=>!p.date_achieved||!N.date_achieved?0:new Date(N.date_achieved).getTime()-new Date(p.date_achieved).getTime())),g}catch(g){return i.set(g.message||"添加成就失败。"),null}finally{e.set(!1)}},updateAchievement:async(c,g)=>{const m=typeof c=="string"?parseInt(c,10):c;if(isNaN(m))return i.set("无效的成就ID进行更新。"),null;e.set(!0),i.set(null);try{const p=await je.updateAchievement(m,g);return p&&d.update(N=>N.map(W=>W.id===m?p:W).sort((W,X)=>!W.date_achieved||!X.date_achieved?0:new Date(X.date_achieved).getTime()-new Date(W.date_achieved).getTime())),p}catch(p){return i.set(p.message||"更新成就失败。"),null}finally{e.set(!1)}},deleteAchievement:async c=>{const g=typeof c=="string"?parseInt(c,10):c;if(isNaN(g))return i.set("无效的成就ID进行删除。"),!1;e.set(!0),i.set(null);try{return await je.deleteAchievement(g),d.update(m=>m.filter(p=>p.id!==g)),!0}catch(m){return i.set(m.message||"删除成就失败。"),!1}finally{e.set(!1)}},getAchievementById:c=>{const g=typeof c=="string"?parseInt(c,10):c;if(!isNaN(g))return De(d).find(m=>m.id===g)}}},O=xt();rt.subscribe(d=>{if(d.user){const e=De(O.isLoading),i=De(O.achievements);!e&&i.length===0&&O.loadAchievements()}else O.achievements.set([])});const pt=(d,e)=>{var i;d.stopPropagation(),(i=e.edit)==null||i.call(e,e.achievement)},bt=async(d,e,i)=>{if(d.stopPropagation(),!!confirm(`Are you sure you want to delete "${e.achievement.title}"? This action cannot be undone.`)){l(i,!0);try{await O.deleteAchievement(e.achievement.id)}catch(x){alert(`Delete failed: ${x.message||"Unknown error"}`),console.error("Error deleting achievement:",x)}finally{l(i,!1)}}},_t=(d,e)=>{var i;(d.key==="Enter"||d.key===" ")&&(d.preventDefault(),(i=e.onClick)==null||i.call(e,e.achievement))};var wt=(d,e)=>{var i;return(i=e.onClick)==null?void 0:i.call(e,e.achievement)},yt=be('<svg aria-hidden="true" role="status" class="inline w-4 h-4 text-red-500 animate-spin" viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z" fill="#E5E7EB"></path><path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0492C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z" fill="currentColor"></path></svg>'),kt=be('<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>'),Ct=k('<p class="text-xs text-gray-500 dark:text-gray-400 mt-1"> </p>'),At=k('<span class="px-2 py-0.5 text-xs font-medium text-purple-700 bg-purple-100 rounded-full dark:bg-purple-900 dark:text-purple-300"> </span>'),Dt=k('<div class="mb-3"><h5 class="text-sm font-semibold text-gray-600 dark:text-gray-400 mb-1">Core Skills:</h5> <div class="flex flex-wrap gap-1.5"></div></div>'),Mt=k('<div class="mb-3"><h5 class="text-sm font-semibold text-gray-600 dark:text-gray-400 mb-1">Description:</h5> <p class="text-gray-700 dark:text-gray-300 text-sm"> </p></div>'),Lt=k('<div class="mb-3"><h5 class="text-sm font-semibold text-gray-600 dark:text-gray-400 mb-1">Results:</h5> <p class="text-gray-700 dark:text-gray-300 text-sm"> </p></div>'),jt=k('<div class="bg-white dark:bg-gray-800 shadow-md rounded-lg border border-purple-200 dark:border-purple-800 overflow-hidden cursor-pointer hover:shadow-lg transition-shadow" role="button" tabindex="0"><div class="border-b border-purple-100 dark:border-purple-800 p-4"><div class="flex justify-between items-center"><h4 class="text-lg font-semibold text-purple-700 dark:text-purple-300"> </h4> <div class="flex space-x-1"><button class="p-1.5 text-sm font-medium text-purple-600 hover:text-purple-800 dark:text-purple-400 dark:hover:text-purple-300 focus:outline-none focus:ring-2 focus:ring-purple-500 rounded-md"><svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor"><path d="M17.414 2.586a2 2 0 00-2.828 0L7 10.172V13h2.828l7.586-7.586a2 2 0 000-2.828z"></path><path fill-rule="evenodd" d="M2 6a2 2 0 012-2h4a1 1 0 010 2H4v10h10v-4a1 1 0 112 0v4a2 2 0 01-2 2H4a2 2 0 01-2-2V6z" clip-rule="evenodd"></path></svg></button> <button class="p-1.5 text-sm font-medium text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 focus:outline-none focus:ring-2 focus:ring-red-500 rounded-md disabled:opacity-50"><!></button></div></div> <!></div> <div class="p-4"><!> <!> <!></div> <div class="px-4 py-2 bg-purple-50 dark:bg-purple-900/20 text-xs text-gray-500 dark:text-gray-400 border-t border-purple-100 dark:border-purple-800"><div class="flex justify-between"><span> </span> <span> <!></span></div></div></div>');function St(d,e){ze(e,!0);let i=U(!1);function x(v){if(!v)return"日期未指定";try{const u=new Date(v),z=u.getTimezoneOffset()*6e4;return new Date(u.getTime()+z).toLocaleDateString(void 0,{year:"numeric",month:"long",day:"numeric"})}catch{return v}}var _=jt();_.__click=[wt,e],_.__keydown=[_t,e];var s=r(_),y=r(s),V=r(y),L=r(V,!0);t(V);var c=n(V,2),g=r(c);g.__click=[pt,e];var m=n(g,2);m.__click=[bt,e,i];var p=r(m);{var N=v=>{var u=yt();h(v,u)},W=v=>{var u=kt();h(v,u)};M(p,v=>{a(i)?v(N):v(W,!1)})}t(m),t(c),t(y);var X=n(y,2);{var ne=v=>{var u=Ct(),z=r(u);t(u),S(Y=>A(z,`Achieved on: ${Y??""}`),[()=>x(e.achievement.date_achieved)]),h(v,u)};M(X,v=>{e.achievement.date_achieved&&v(ne)})}t(s);var E=n(s,2),Z=r(E);{var ae=v=>{var u=Dt(),z=n(r(u),2);Ze(z,21,()=>e.achievement.core_skills_json,$e,(Y,me)=>{var fe=At(),xe=r(fe,!0);t(fe),S(()=>A(xe,a(me))),h(Y,fe)}),t(z),t(u),h(v,u)};M(Z,v=>{e.achievement.core_skills_json&&e.achievement.core_skills_json.length>0&&v(ae)})}var de=n(Z,2);{var P=v=>{var u=Mt(),z=n(r(u),2),Y=r(z,!0);t(z),t(u),S(()=>A(Y,e.achievement.description)),h(v,u)};M(de,v=>{e.achievement.description&&v(P)})}var G=n(de,2);{var J=v=>{var u=Lt(),z=n(r(u),2),Y=r(z,!0);t(z),t(u),S(()=>A(Y,e.achievement.quantifiable_results)),h(v,u)};M(G,v=>{e.achievement.quantifiable_results&&v(J)})}t(E);var ie=n(E,2),q=r(ie),Q=r(q),$=r(Q);t(Q);var ee=n(Q,2),ce=r(ee),he=n(ce);{var ge=v=>{var u=Je();S(z=>A(u,`(Updated: ${z??""})`),[()=>x(e.achievement.updated_at)]),h(v,u)};M(he,v=>{e.achievement.updated_at&&e.achievement.updated_at!==e.achievement.created_at&&v(ge)})}t(ee),t(q),t(ie),t(_),S(v=>{Ne(_,"aria-label",`View details for achievement: ${e.achievement.title??""}`),A(L,e.achievement.title),Ne(g,"aria-label",`Edit achievement ${e.achievement.title??""}`),m.disabled=a(i),Ne(m,"aria-label",`Delete achievement ${e.achievement.title??""}`),A($,`ID: ${e.achievement.id??""}`),A(ce,`${v??""} `)},[()=>x(e.achievement.created_at)]),h(d,_),Ee()}Be(["click","keydown"]);var zt=k('<div class="text-center py-6"><div role="status" class="flex justify-center items-center"><svg aria-hidden="true" class="w-10 h-10 text-purple-200 animate-spin dark:text-purple-700 fill-purple-600" viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z" fill="currentColor"></path><path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0492C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z" fill="lightgray"></path></svg> <span class="sr-only">Loading...</span></div> <p class="mt-2 text-purple-600 dark:text-purple-400">Loading achievements...</p></div>'),Et=()=>O.loadAchievements(),Bt=k('<div class="p-4 mx-2 mb-4 text-sm text-red-700 bg-red-100 rounded-lg dark:bg-red-200 dark:text-red-800 text-center" role="alert"><span class="font-medium">Loading error:</span> <button class="ml-4 px-3 py-1.5 text-sm font-medium text-white bg-purple-600 rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500">Retry</button></div>'),Tt=k('<div class="text-center py-6"><svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-purple-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1"><path stroke-linecap="round" stroke-linejoin="round" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16l3.5-2 3.5 2 3.5-2 3.5 2zM12 11v2m0-4h.01"></path></svg> <h3 class="mt-2 text-lg font-medium text-purple-900 dark:text-purple-100">No achievements yet</h3> <p class="mt-1 text-sm text-purple-600 dark:text-purple-400">Start adding your first achievement!</p></div>'),Vt=k('<div class="space-y-4 px-2"></div>'),qt=k('<div class="flex flex-col h-full py-2"><div class="flex-grow"><!></div></div>');function It(d,e){ze(e,!0);const[i,x]=tt(),_=()=>Se(L,"$achievements",i),s=()=>Se(c,"$isLoading",i),y=()=>Se(g,"$error",i);let V=et(e,"onSelectAchievement",3,E=>{});const L=O.achievements,c=O.isLoading,g=O.error;ut(()=>{_().length===0&&!s()&&!y()&&O.loadAchievements()});function m(E){var Z;(Z=e.editAchievement)==null||Z.call(e,E)}var p=qt(),N=r(p),W=r(N);{var X=E=>{var Z=zt();h(E,Z)},ne=(E,Z)=>{{var ae=P=>{var G=Bt(),J=n(r(G)),ie=n(J);ie.__click=[Et],t(G),S(()=>A(J,` ${y()??""} `)),h(P,G)},de=(P,G)=>{{var J=q=>{var Q=Tt();h(q,Q)},ie=q=>{var Q=Vt();Ze(Q,5,_,$=>$.id,($,ee)=>{St($,{get achievement(){return a(ee)},edit:m,onClick:()=>V()(a(ee))})}),t(Q),h(q,Q)};M(P,q=>{_().length===0?q(J):q(ie,!1)},G)}};M(E,P=>{y()?P(ae):P(de,!1)},Z)}};M(W,E=>{s()&&_().length===0?E(X):E(ne,!1)})}t(N),t(p),h(d,p),Ee(),x()}Be(["click"]);const Nt=(d,e,i,x,_)=>{var s;e()?(s=i.cancel)==null||s.call(i):(x(),l(_,null))};var Ht=k('<div class="p-3 mb-4 text-sm text-red-700 bg-red-100 rounded-lg dark:bg-red-200 dark:text-red-800" role="alert"><span class="font-medium">Error:</span> </div>'),Zt=k('<div class="p-3 mb-4 text-sm text-green-700 bg-green-100 rounded-lg dark:bg-green-200 dark:text-green-800" role="alert"><span class="font-medium">Success:</span> </div>'),Pt=be('<svg aria-hidden="true" role="status" class="inline w-4 h-4 me-3 text-white animate-spin" viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z" fill="#E5E7EB"></path><path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0492C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z" fill="currentColor"></path></svg> Processing...',1),Ft=k('<form class="space-y-6 p-4 bg-white dark:bg-gray-800 shadow-md rounded-lg"><h3 class="text-xl font-semibold text-gray-900 dark:text-white"> </h3> <!> <!> <div><label for="title-achv" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Title <span class="text-red-500">*</span></label> <input type="text" id="title-achv" required class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" placeholder="e.g., Completed Phase 1 of Project X"></div> <div><label for="description-achv" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Description</label> <textarea id="description-achv" rows="3" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" placeholder="Describe this achievement in detail..."></textarea></div> <div><label for="quantifiable_results-achv" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Quantifiable Results</label> <input type="text" id="quantifiable_results-achv" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" placeholder="e.g., Improved efficiency by 20%, saved $10,000 in costs"></div> <div><label for="core_skills_input-achv" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Core Skills (comma separated)</label> <input type="text" id="core_skills_input-achv" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" placeholder="e.g., Communication, Leadership, Svelte"></div> <div><label for="date_achieved-achv" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Date Achieved</label> <input type="date" id="date_achieved-achv" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"></div> <div class="flex justify-end space-x-3 pt-4"><button type="button" class="py-2.5 px-5 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"> </button> <button type="submit" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800 disabled:opacity-50"><!></button></div></form>');function Ot(d,e){var C,I,F,oe,le,ve;ze(e,!0);const[i,x]=tt(),_=()=>Se(W,"$isLoadingStore",i);let s=et(e,"achievement",3,null),y=U(ye(((C=s())==null?void 0:C.title)||"")),V=U(ye(((I=s())==null?void 0:I.description)||"")),L=U(ye(((F=s())==null?void 0:F.quantifiable_results)||"")),c=U(ye(((le=(oe=s())==null?void 0:oe.core_skills_json)==null?void 0:le.join(", "))||"")),g=U(ye(((ve=s())==null?void 0:ve.date_achieved)||"")),m=U(null),p=U(null),N=U(!1);const W=O.isLoading;let X=Ye(_);ot(()=>{var o;s()?(l(y,s().title||"",!0),l(V,s().description||"",!0),l(L,s().quantifiable_results||"",!0),l(c,((o=s().core_skills_json)==null?void 0:o.join(", "))||"",!0),l(g,s().date_achieved||"",!0),l(m,null),l(p,null)):(l(y,""),l(V,""),l(L,""),l(c,""),l(g,""))});const ne=async()=>{var D,te;if(l(m,null),l(p,null),l(N,!0),!a(y).trim()){l(m,"Title cannot be empty."),l(N,!1);return}const o=a(c).split(",").map(K=>K.trim()).filter(K=>K!=="");if(s()&&s().id){const K={title:a(y),description:a(V)||null,quantifiable_results:a(L)||null,core_skills_json:o.length>0?o:void 0,date_achieved:a(g)||null};try{const H=await O.updateAchievement(s().id,K);H?(l(p,"Achievement updated successfully!"),(D=e.save)==null||D.call(e,H)):l(m,De(O.error)||"Failed to update achievement.",!0)}catch(H){l(m,H.message||"An unknown error occurred during update.",!0)}}else{const K={title:a(y),description:a(V)||null,quantifiable_results:a(L)||null,core_skills_json:o.length>0?o:void 0,date_achieved:a(g)||null};try{const H=await O.addAchievement(K);H?(l(p,"Achievement added successfully!"),(te=e.save)==null||te.call(e,H),E()):l(m,De(O.error)||"Failed to add achievement.",!0)}catch(H){l(m,H.message||"An unknown error occurred during creation.",!0)}}l(N,!1)},E=()=>{l(y,""),l(V,""),l(L,""),l(c,""),l(g,""),l(m,null)};var Z=Ft(),ae=r(Z),de=r(ae,!0);t(ae);var P=n(ae,2);{var G=o=>{var D=Ht(),te=n(r(D));t(D),S(()=>A(te,` ${a(m)??""}`)),h(o,D)};M(P,o=>{a(m)&&o(G)})}var J=n(P,2);{var ie=o=>{var D=Zt(),te=n(r(D));t(D),S(()=>A(te,` ${a(p)??""}`)),h(o,D)};M(J,o=>{a(p)&&o(ie)})}var q=n(J,2),Q=n(r(q),2);Le(Q),t(q);var $=n(q,2),ee=n(r($),2);vt(ee),t($);var ce=n($,2),he=n(r(ce),2);Le(he),t(ce);var ge=n(ce,2),v=n(r(ge),2);Le(v),t(ge);var u=n(ge,2),z=n(r(u),2);Le(z),t(u);var Y=n(u,2),me=r(Y);me.__click=[Nt,s,e,E,p];var fe=r(me,!0);t(me);var xe=n(me,2),Te=r(xe);{var Ve=o=>{var D=Pt();He(),h(o,D)},f=o=>{var D=Je();S(()=>A(D,s()?"Save Changes":"Add Achievement")),h(o,D)};M(Te,o=>{a(N)||a(X)?o(Ve):o(f,!1)})}t(xe),t(Y),t(Z),S(()=>{A(de,s()?"Edit Achievement":"Add New Achievement"),A(fe,s()?"Cancel":"Reset"),xe.disabled=a(N)||a(X)}),ct("submit",Z,o=>{o.preventDefault(),ne()}),Ce(Q,()=>a(y),o=>l(y,o)),Ce(ee,()=>a(V),o=>l(V,o)),Ce(he,()=>a(L),o=>l(L,o)),Ce(v,()=>a(c),o=>l(c,o)),Ce(z,()=>a(g),o=>l(g,o)),h(d,Z),Ee(),x()}Be(["click"]);async function Rt(d,e,i,x,_){if(a(e)&&confirm(`Are you sure you want to delete "${a(e).title}"? This action cannot be undone.`)){l(i,!0),l(x,null);try{await O.deleteAchievement(a(e).id)?(l(x,{type:"success",message:`Successfully deleted "${a(e).title}"`},!0),l(e,null),l(_,!1),setTimeout(()=>{l(x,null)},3e3)):l(x,{type:"error",message:"Failed to delete achievement. Please try again."},!0)}catch(s){l(x,{type:"error",message:`Failed to delete: ${s.message||"Unknown error"}`},!0)}finally{l(i,!1)}}}var Ut=be('<svg class="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path></svg>'),Qt=be('<svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path></svg>'),Kt=(d,e)=>l(e,null),Wt=k('<div role="alert"><div class="flex"><div class="flex-shrink-0"><!></div> <div class="ml-3"><p class="text-sm font-medium"> </p></div> <div class="ml-auto pl-3"><div class="-mx-1.5 -my-1.5"><button><span class="sr-only">Dismiss</span> <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg></button></div></div></div></div>'),Xt=(d,e,i)=>a(e)&&i(a(e)),Gt=be('<svg class="animate-spin h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>'),Jt=be('<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 6h18"></path><path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6"></path><path d="M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path><line x1="10" y1="11" x2="10" y2="17"></line><line x1="14" y1="11" x2="14" y2="17"></line></svg>'),Yt=k('<div class="mb-6"><div class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200"><svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect><line x1="16" y1="2" x2="16" y2="6"></line><line x1="8" y1="2" x2="8" y2="6"></line><line x1="3" y1="10" x2="21" y2="10"></line></svg> </div></div>'),$t=k('<span class="px-3 py-1 text-sm font-medium text-purple-700 bg-purple-100 rounded-full dark:bg-purple-900 dark:text-purple-300"> </span>'),er=k('<div class="mb-6"><h3>Core Skills</h3> <div class="flex flex-wrap gap-2"></div></div>'),tr=k('<div class="mb-6"><h3>Description</h3> <div class="bg-white dark:bg-gray-700 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600"><p class="text-gray-700 dark:text-gray-300"> </p></div></div>'),rr=k('<div class="mb-6"><h3>Quantifiable Results</h3> <div class="bg-white dark:bg-gray-700 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600"><p class="text-gray-700 dark:text-gray-300"> </p></div></div>'),ar=k('<span class="ml-2"> </span>'),ir=k('<div><div class="flex justify-between items-center mb-6"><h1> </h1> <div class="flex space-x-2"><button aria-label="Edit achievement"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path><path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path></svg></button> <button class="p-2 text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed" aria-label="Delete achievement"><!></button></div></div> <!> <!> <!> <!> <div class="mt-8 pt-4 border-t border-gray-200 dark:border-gray-700"><div class="flex justify-between text-sm text-gray-500 dark:text-gray-400"><span> </span> <span> <!></span></div></div></div>'),lr=k('<div class="flex flex-col items-center justify-center h-full"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" fill="currentColor"><path d="M256 512A256 256 0 1 0 256 0a256 256 0 1 0 0 512zM369 209L241 337c-9.4 9.4-24.6 9.4-33.9 0l-64-64c-9.4-9.4-9.4-24.6 0-33.9s24.6-9.4 33.9 0l47 47L335 175c9.4-9.4 24.6-9.4 33.9 0s9.4 24.6 0 33.9z"></path></svg> <h3>Select an Achievement</h3> <p class="text-gray-600 dark:text-gray-400 text-center max-w-md">Select an achievement from the list to view details, or click the "+" button to add a new achievement.</p></div>'),sr=k('<div><!> <div class="flex-grow overflow-hidden"><div><div><div><div class="p-4 border-b border-gray-200 dark:border-gray-700"><div class="flex justify-between items-center"><h2><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" fill="currentColor"><path d="M256 512A256 256 0 1 0 256 0a256 256 0 1 0 0 512zM369 209L241 337c-9.4 9.4-24.6 9.4-33.9 0l-64-64c-9.4-9.4-9.4-24.6 0-33.9s24.6-9.4 33.9 0l47 47L335 175c9.4-9.4 24.6-9.4 33.9 0s9.4 24.6 0 33.9z"></path></svg> Achievements</h2> <button aria-label="Add new achievement"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"></path></svg></button></div></div> <div><div></div> <div><!></div></div></div></div> <div><div><div class="flex-grow overflow-hidden"><div class="h-full overflow-y-auto p-6"><!></div></div></div></div></div></div> <!></div>');function wr(d,e){ze(e,!0);const i=gt.done;let x=U(!1),_=U(null),s=U(null),y=U(!1),V=U(!1),L=U(null);function c(){l(_,null),l(x,!0),l(y,!1)}function g(f){l(_,f,!0),l(x,!0),l(y,!1)}function m(f){l(s,f,!0),l(y,!0),l(_,null),l(x,!1)}function p(){l(x,!1),l(_,null)}function N(f){p(),a(s)&&a(s).id===f.id&&l(s,f,!0),a(_)||(l(s,f,!0),l(y,!0))}function W(){p()}function X(f){if(!f)return"Date not specified";try{const C=new Date(f),I=C.getTimezoneOffset()*6e4;return new Date(C.getTime()+I).toLocaleDateString(void 0,{year:"numeric",month:"long",day:"numeric"})}catch{return f}}var ne=sr(),E=r(ne);{var Z=f=>{var C=Wt(),I=r(C),F=r(I),oe=r(F);{var le=ue=>{var _e=Ut();h(ue,_e)},ve=ue=>{var _e=Qt();h(ue,_e)};M(oe,ue=>{a(L).type==="success"?ue(le):ue(ve,!1)})}t(F);var o=n(F,2),D=r(o),te=r(D,!0);t(D),t(o);var K=n(o,2),H=r(K),Me=r(H);Me.__click=[Kt,L],t(H),t(K),t(I),t(C),S(()=>{j(C,1,`mb-4 p-4 rounded-md ${a(L).type==="success"?"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300":"bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300"}`),A(te,a(L).message),j(Me,1,`inline-flex rounded-md p-1.5 ${a(L).type==="success"?"text-green-500 hover:bg-green-100 dark:text-green-300 dark:hover:bg-green-900/50":"text-red-500 hover:bg-red-100 dark:text-red-300 dark:hover:bg-red-900/50"} focus:outline-none focus:ring-2 focus:ring-offset-2 ${a(L).type==="success"?"focus:ring-green-500":"focus:ring-red-500"}`)}),h(f,C)};M(E,f=>{a(L)&&f(Z)})}var ae=n(E,2),de=r(ae),P=r(de),G=r(P),J=r(G),ie=r(J),q=r(ie),Q=r(q);He(),t(q);var $=n(q,2);$.__click=c,t(ie),t(J);var ee=n(J,2),ce=r(ee),he=n(ce,2),ge=r(he);It(ge,{addNewAchievement:c,editAchievement:g,onSelectAchievement:m}),t(he),t(ee),t(G),t(P);var v=n(P,2),u=r(v),z=r(u),Y=r(z),me=r(Y);{var fe=f=>{var C=ir(),I=r(C),F=r(I),oe=r(F,!0);t(F);var le=n(F,2),ve=r(le);ve.__click=[Xt,s,g];var o=n(ve,2);o.__click=[Rt,s,V,L,y];var D=r(o);{var te=b=>{var w=Gt();h(b,w)},K=b=>{var w=Jt();h(b,w)};M(D,b=>{a(V)?b(te):b(K,!1)})}t(o),t(le),t(I);var H=n(I,2);{var Me=b=>{var w=Yt(),R=r(w),re=n(r(R));t(R),t(w),S(se=>A(re,` Achieved on: ${se??""}`),[()=>X(a(s).date_achieved)]),h(b,w)};M(H,b=>{a(s).date_achieved&&b(Me)})}var ue=n(H,2);{var _e=b=>{var w=er(),R=r(w),re=n(R,2);Ze(re,21,()=>a(s).core_skills_json,$e,(se,we)=>{var pe=$t(),dt=r(pe,!0);t(pe),S(()=>A(dt,a(we))),h(se,pe)}),t(re),t(w),S(se=>j(R,1,se),[()=>B(T("text-lg font-semibold mb-2",i.text))]),h(b,w)};M(ue,b=>{a(s).core_skills_json&&a(s).core_skills_json.length>0&&b(_e)})}var Pe=n(ue,2);{var at=b=>{var w=tr(),R=r(w),re=n(R,2),se=r(re),we=r(se,!0);t(se),t(re),t(w),S(pe=>{j(R,1,pe),A(we,a(s).description)},[()=>B(T("text-lg font-semibold mb-2",i.text))]),h(b,w)};M(Pe,b=>{a(s).description&&b(at)})}var Fe=n(Pe,2);{var it=b=>{var w=rr(),R=r(w),re=n(R,2),se=r(re),we=r(se,!0);t(se),t(re),t(w),S(pe=>{j(R,1,pe),A(we,a(s).quantifiable_results)},[()=>B(T("text-lg font-semibold mb-2",i.text))]),h(b,w)};M(Fe,b=>{a(s).quantifiable_results&&b(it)})}var Oe=n(Fe,2),Re=r(Oe),qe=r(Re),lt=r(qe);t(qe);var Ue=n(qe,2),Qe=r(Ue),st=n(Qe);{var nt=b=>{var w=ar(),R=r(w);t(w),S(re=>A(R,`(Updated: ${re??""})`),[()=>X(a(s).updated_at)]),h(b,w)};M(st,b=>{a(s).updated_at&&a(s).updated_at!==a(s).created_at&&b(nt)})}t(Ue),t(Re),t(Oe),t(C),S((b,w,R)=>{j(F,1,b),A(oe,a(s).title),j(ve,1,w),o.disabled=a(V),A(lt,`ID: ${a(s).id??""}`),A(Qe,`Created: ${R??""} `)},[()=>B(T(Xe.h1,i.text)),()=>B(T("p-2 rounded-md focus:outline-none focus:ring-2",i.text,i.hover)),()=>X(a(s).created_at)]),h(f,C)},xe=f=>{var C=lr(),I=r(C),F=n(I,2);He(2),t(C),S((oe,le)=>{j(I,0,oe),j(F,1,le)},[()=>B(T("h-16 w-16 mb-4",i.icon)),()=>B(T("text-xl font-medium mb-2",i.text))]),h(f,C)};M(me,f=>{a(s)&&a(y)?f(fe):f(xe,!1)})}t(Y),t(z),t(u),t(v),t(de),t(ae);var Te=n(ae,2);{var Ve=f=>{const C=Ye(()=>a(_)?"Edit Achievement":"Add New Achievement");ft(f,{get isOpen(){return a(x)},close:p,get title(){return a(C)},modalWidth:"max-w-xl",children:(I,F)=>{Ot(I,{get achievement(){return a(_)},save:N,cancel:W})},$$slots:{default:!0}})};M(Te,f=>{a(x)&&f(Ve)})}t(ne),S((f,C,I,F,oe,le,ve,o,D,te,K,H)=>{j(ne,1,f),j(de,1,C),j(P,1,I),j(G,1,F),j(q,1,oe),j(Q,0,le),j($,1,ve),j(ee,1,o),j(ce,1,D),j(he,1,te),j(v,1,K),j(u,1,H)},[()=>B(T(ht,"h-[calc(100vh-180px)] flex flex-col")),()=>B(T(mt.twoColumnOneThree,"h-full")),()=>B(T(Ke.oneFourth,"h-full flex flex-col")),()=>B(T(We,i.border,"h-full flex flex-col")),()=>B(T(Xe.h3,i.text)),()=>B(T("h-5 w-5 mr-2 inline",i.icon)),()=>B(T("p-1 text-sm rounded-md focus:outline-none focus:ring-2",i.text,i.hover)),()=>B(T(Ge.container,"flex-grow relative")),()=>B(T(Ge.indicator,"left-0",i.scrollbar)),()=>B(T("pl-3","absolute inset-0 overflow-y-auto pr-2")),()=>B(T(Ke.threeFourths,"h-full flex flex-col")),()=>B(T(We,i.border,"h-full flex flex-col"))]),h(d,ne),Ee()}Be(["click"]);export{wr as component};
