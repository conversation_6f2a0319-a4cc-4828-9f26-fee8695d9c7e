import{t as u,a as p}from"../chunks/CEORzCZH.js";import"../chunks/Dj8TIuzp.js";import{p as V,t as M,i as a,a as W,s as m,c as i,x as e,w as g,r as f,n as X}from"../chunks/B9CKrN7X.js";import{e as Z,s as Y}from"../chunks/Cc0s-Eqn.js";import{i as j}from"../chunks/C9zVtFY0.js";import{r as _}from"../chunks/DUXSSeDn.js";import{b as w}from"../chunks/Bk_lFxuP.js";import{p as $}from"../chunks/CWmzcjye.js";import{i as ee}from"../chunks/ZU85TcGt.js";import{o as se}from"../chunks/D6jnoBFN.js";import{g as z}from"../chunks/DgxGL_Xb.js";import{a as ae}from"../chunks/CjIxvcxe.js";import{i as re}from"../chunks/CaTJm4T8.js";var te=u('<div class="error-message svelte-12mlfgf"><p class="svelte-12mlfgf"> </p></div>'),le=u('<div class="success-message svelte-12mlfgf"><p class="svelte-12mlfgf"> </p></div>'),oe=u('<span class="svelte-12mlfgf">Creating Account...</span>'),ie=u('<span class="svelte-12mlfgf">Sign Up</span>'),fe=u('<div class="register-container svelte-12mlfgf"><div class="register-card svelte-12mlfgf"><h1 class="card-title svelte-12mlfgf">Create Account</h1> <p class="card-subtitle svelte-12mlfgf">Join us! Fill in the details below to get started.</p> <form class="register-form svelte-12mlfgf"><div class="form-group svelte-12mlfgf"><label for="username" class="svelte-12mlfgf">Username</label> <input type="text" id="username" required placeholder="your_username" class="svelte-12mlfgf"></div> <div class="form-group svelte-12mlfgf"><label for="email" class="svelte-12mlfgf">Email Address</label> <input type="email" id="email" required placeholder="<EMAIL>" class="svelte-12mlfgf"></div> <div class="form-group svelte-12mlfgf"><label for="password" class="svelte-12mlfgf">Password</label> <input type="password" id="password" required placeholder="•••••••• (min. 8 characters)" class="svelte-12mlfgf"></div> <div class="form-group svelte-12mlfgf"><label for="confirmPassword" class="svelte-12mlfgf">Confirm Password</label> <input type="password" id="confirmPassword" required placeholder="••••••••" class="svelte-12mlfgf"></div> <!> <!> <button type="submit" class="submit-button svelte-12mlfgf"><!></button></form> <div class="alternative-action svelte-12mlfgf"><p class="svelte-12mlfgf">Already have an account? <a href="/login" class="svelte-12mlfgf">Log in here</a></p></div></div></div>');function xe(B,G){V(G,!1);let d=g(""),v=g(""),c=g(""),b=g(""),l=g(""),h=g(""),o=g(!1);se(()=>re.subscribe(t=>{t&&z("/doing")}));async function H(){var t;if(e(o,!0),e(l,""),e(h,""),console.log("Register form submitted with:",{username:a(d),email:a(v),passwordLength:a(c).length}),a(c)!==a(b)){e(l,"Passwords do not match."),e(o,!1);return}if(a(c).length<8){e(l,"Password must be at least 8 characters long."),e(o,!1);return}if(!a(d)||a(d).trim().length<3){e(l,"Username must be at least 3 characters long."),e(o,!1);return}if(!a(v)||!a(v).includes("@")){e(l,"Please enter a valid email address."),e(o,!1);return}const s={username:a(d).trim(),email:a(v).trim(),password:a(c)};console.log("Sending registration request with payload:",{username:s.username,email:s.email,passwordLength:s.password.length});try{console.log("Calling authService.registerUser with payload");const n=await ae.registerUser(s);console.log("Registration successful:",n),e(h,n.message+" You can now log in."),e(d,""),e(v,""),e(c,""),e(b,""),setTimeout(()=>{z("/login")},2e3)}catch(n){console.error("Registration error (raw):",n);const r=n;if(console.error("Registration error details:",{status:r.status,message:r.message,data:r.data}),r.status===409)r.data&&r.data.error&&r.data.error.includes("Username")?e(l,"用户名已被占用，请尝试其他用户名。"):r.data&&r.data.error&&r.data.error.includes("Email")?e(l,"该邮箱已注册，请直接登录或使用其他邮箱。"):e(l,"用户名或邮箱已被占用，请尝试其他信息。");else if(r.data&&r.data.message)e(l,r.data.message);else if((t=r.data)!=null&&t.error){let J=[];if(typeof r.data.error=="object"){for(const T in r.data.error)J.push(`${T}: ${r.data.error[T]}`);e(l,J.join("; "))}else e(l,r.data.error)}else r.message?e(l,r.message):e(l,"注册过程中发生意外错误，请稍后重试。")}finally{e(o,!1)}}ee();var y=fe(),k=i(y),x=m(i(k),4),P=i(x),U=m(i(P),2);_(U),f(P);var q=m(P,2),A=m(i(q),2);_(A),f(q);var R=m(q,2),C=m(i(R),2);_(C),f(R);var L=m(R,2),S=m(i(L),2);_(S),f(L);var D=m(L,2);{var I=s=>{var t=te(),n=i(t),r=i(n,!0);f(n),f(t),M(()=>Y(r,a(l))),p(s,t)};j(D,s=>{a(l)&&s(I)})}var F=m(D,2);{var K=s=>{var t=le(),n=i(t),r=i(n,!0);f(n),f(t),M(()=>Y(r,a(h))),p(s,t)};j(F,s=>{a(h)&&s(K)})}var E=m(F,2),N=i(E);{var O=s=>{var t=oe();p(s,t)},Q=s=>{var t=ie();p(s,t)};j(N,s=>{a(o)?s(O):s(Q,!1)})}f(E),f(x),X(2),f(k),f(y),M(()=>{U.disabled=a(o),A.disabled=a(o),C.disabled=a(o),S.disabled=a(o),E.disabled=a(o)}),w(U,()=>a(d),s=>e(d,s)),w(A,()=>a(v),s=>e(v,s)),w(C,()=>a(c),s=>e(c,s)),w(S,()=>a(b),s=>e(b,s)),Z("submit",x,$(H)),p(B,y),W()}export{xe as component};
