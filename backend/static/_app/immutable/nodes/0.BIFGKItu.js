import{t as x,a as h}from"../chunks/CEORzCZH.js";import"../chunks/Dj8TIuzp.js";import{p as A,a as O,i as c,Q as M,r as g,c as k,s as $,t as j,aK as F,aL as T,f as B}from"../chunks/B9CKrN7X.js";import{e as V,t as H,f as U,s as q}from"../chunks/D8moldTO.js";import{i as K}from"../chunks/ZU85TcGt.js";import{a as D,s as E}from"../chunks/BIYeehul.js";/* empty css                */import{o as Q}from"../chunks/D6jnoBFN.js";import{s as J,g as y}from"../chunks/DgxGL_Xb.js";import{i as X}from"../chunks/CaTJm4T8.js";import{t as Y}from"../chunks/BCVqPHae.js";import{d as Z,s as S}from"../chunks/Cc0s-Eqn.js";import{i as ee}from"../chunks/C9zVtFY0.js";import{s as L}from"../chunks/BIuBsydj.js";import{n as I}from"../chunks/Cy9cqOeW.js";const re=()=>{const e=J;return{page:{subscribe:e.page.subscribe},navigating:{subscribe:e.navigating.subscribe},updated:e.updated}},te={subscribe(e){return re().page.subscribe(e)}};function oe(e){return new Promise((t,o)=>{const i=new Image;i.onload=()=>t(i),i.onerror=n=>o(n),i.src=e})}function ae(e){return Promise.all(e.map(oe))}function se(){return ae(["/favicon.png","/images/default-background.jpg"])}const ne={TODO:"/images/icons/todo.svg",DOING:"/images/icons/doing.svg",DONE:"/images/icons/done.svg",PLAN:"/images/icons/plan.svg",ANCHOR:"/images/icons/anchor.svg",ADD:"/images/icons/add.svg",EDIT:"/images/icons/edit.svg",DELETE:"/images/icons/delete.svg",CLOSE:"/images/icons/close.svg",MENU:"/images/icons/menu.svg",SEARCH:"/images/icons/search.svg",SUCCESS:"/images/icons/success.svg",ERROR:"/images/icons/error.svg",WARNING:"/images/icons/warning.svg",INFO:"/images/icons/info.svg"};function ie(e=Object.values(ne)){return Promise.all(e.map(t=>fetch(t).then(o=>{o.ok||console.warn(`Failed to preload SVG: ${t}`)}).catch(o=>{console.error(`Error preloading SVG: ${t}`,o)})))}function N(e,t){if(performance.mark(e),t)try{performance.measure(t,e)}catch(o){console.error("Error creating performance measure:",o)}}async function ce(){N("app-initialization-start"),de();try{await se()}catch(e){console.error("Failed to preload critical images:",e)}try{await ie()}catch(e){console.error("Failed to preload SVG icons:",e)}le(),N("app-initialization-end","app-initialization"),console.log("Application initialized")}function de(){Y.subscribe(e=>{e.darkMode?document.documentElement.classList.add("dark"):document.documentElement.classList.remove("dark"),e.customBackground?(document.documentElement.style.setProperty("--custom-background",`url(${e.customBackground})`),document.documentElement.classList.add("has-custom-bg"),document.body.classList.add("has-background"),document.body.style.backgroundColor="transparent",document.documentElement.style.backgroundColor="transparent",e.darkMode?document.body.classList.add("dark-with-background"):document.body.classList.remove("dark-with-background")):(document.documentElement.style.setProperty("--custom-background","none"),document.documentElement.classList.remove("has-custom-bg"),document.body.classList.remove("has-background"),document.body.style.backgroundColor="",document.documentElement.style.backgroundColor="")})}function le(){"serviceWorker"in navigator&&window.addEventListener("load",()=>{navigator.serviceWorker.register("/service-worker.js").then(e=>{console.log("ServiceWorker registration successful with scope:",e.scope)}).catch(e=>{console.error("ServiceWorker registration failed:",e)})})}function ue(e){return--e*e*e*e*e+1}var ge=(e,t,o)=>t(c(o).id),me=x('<button type="button" class="ml-3 flex-shrink-0 rounded-full p-1 hover:bg-black/10 dark:hover:bg-white/10 transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-current" aria-label="关闭通知"><svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg></button>'),fe=x('<div><div class="flex items-center gap-3"><div> </div> <span class="text-sm font-medium flex-1"> </span></div> <!></div>'),pe=x('<div class="notification-container fixed top-4 left-1/2 transform -translate-x-1/2 flex flex-col gap-2 pointer-events-none"></div>');function be(e,t){A(t,!0);const[o,i]=D(),n=()=>E(I,"$notificationStore",o),d=M(()=>{try{const r=n();return(r==null?void 0:r.notifications)||[]}catch(r){return console.error("NotificationContainer: 获取通知状态时发生错误",r),[]}});function m(r){const a="flex items-center justify-between p-4 rounded-lg shadow-lg border max-w-md w-full";switch(r){case"success":return`${a} bg-green-100 dark:bg-green-800/80 text-green-900 dark:text-green-100 border-green-300 dark:border-green-600`;case"error":return`${a} bg-red-100 dark:bg-red-800/80 text-red-900 dark:text-red-100 border-red-300 dark:border-red-600`;case"warning":return`${a} bg-yellow-100 dark:bg-yellow-800/80 text-yellow-900 dark:text-yellow-100 border-yellow-300 dark:border-yellow-600`;case"info":return`${a} bg-blue-100 dark:bg-blue-800/80 text-blue-900 dark:text-blue-100 border-blue-300 dark:border-blue-600`;default:return`${a} bg-gray-100 dark:bg-gray-800/80 text-gray-900 dark:text-gray-100 border-gray-300 dark:border-gray-600`}}function f(r){switch(r){case"success":return"text-green-700 dark:text-green-200";case"error":return"text-red-700 dark:text-red-200";case"warning":return"text-yellow-700 dark:text-yellow-200";case"info":return"text-blue-700 dark:text-blue-200";default:return"text-gray-700 dark:text-gray-200"}}function p(r){switch(r){case"success":return"✓";case"error":return"✕";case"warning":return"⚠";case"info":return"ℹ";default:return"•"}}function w(r){try{if(!r){console.warn("NotificationContainer: 尝试关闭无效的通知ID");return}I.removeNotification(r)}catch(a){console.error("NotificationContainer: 关闭通知时发生错误",a)}}var s=pe();V(s,21,()=>c(d),r=>r.id,(r,a)=>{var l=fe(),_=k(l),b=k(_),P=k(b,!0);g(b);var C=$(b,2),z=k(C,!0);g(C),g(_);var R=$(_,2);{var W=u=>{var v=me();v.__click=[ge,w,a],h(u,v)};ee(R,u=>{c(a).dismissible&&u(W)})}g(l),j((u,v,G)=>{L(l,1,`${u??""} pointer-events-auto`),L(b,1,`${v??""} text-lg font-bold`),S(P,G),S(z,c(a).message)},[()=>m(c(a).type),()=>f(c(a).type),()=>p(c(a).type)]),H(3,l,()=>U,()=>({y:-20,duration:300,easing:ue})),h(r,l)}),g(s),h(e,s),O(),i()}Z(["click"]);var ve=x("<!> <!>",1);function De(e,t){A(t,!1);const[o,i]=D(),n=()=>E(X,"$isAuthenticated",o),d=()=>E(te,"$page",o);Q(()=>{ce(),m()});function m(){const s=d().url.pathname;n()&&(s==="/login"||s==="/register")&&y("/doing"),!n()&&s!=="/login"&&s!=="/register"&&y("/login"),(!s||s==="/")&&(n()?y("/doing"):y("/login"))}F(()=>(n(),d()),()=>{(n()!==void 0||d().url.pathname)&&m()}),T(),K();var f=ve(),p=B(f);q(p,t,"default",{},null);var w=$(p,2);be(w,{}),h(e,f),O(),i()}export{De as component};
