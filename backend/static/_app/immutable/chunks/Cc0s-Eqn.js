import{R as P,B as w,ab as V,ah as M,ai as m,aj as E,ak as j,ag as R,o as Y,q as $,al as x,am as L,H as z,an as F,ao as k,J as b,I,D as G,N as h,ap as J,aq as U,ar as K,as as Q,at as X,ad as Z,L as ee,p as te,b as re,a as ae}from"./B9CKrN7X.js";import{b as oe}from"./CEORzCZH.js";function pe(e){return e.endsWith("capture")&&e!=="gotpointercapture"&&e!=="lostpointercapture"}const ne=["beforeinput","click","change","dblclick","contextmenu","focusin","focusout","input","keydown","keyup","mousedown","mousemove","mouseout","mouseover","mouseup","pointerdown","pointermove","pointerout","pointerover","pointerup","touchend","touchmove","touchstart"];function ve(e){return ne.includes(e)}const ie={formnovalidate:"formNoValidate",ismap:"isMap",nomodule:"noModule",playsinline:"playsInline",readonly:"readOnly",defaultvalue:"defaultValue",defaultchecked:"defaultChecked",srcobject:"srcObject",novalidate:"noValidate",allowfullscreen:"allowFullscreen",disablepictureinpicture:"disablePictureInPicture",disableremoteplayback:"disableRemotePlayback"};function he(e){return e=e.toLowerCase(),ie[e]??e}const se=["touchstart","touchmove"];function ue(e){return se.includes(e)}function ye(e,t){if(t){const r=document.body;e.autofocus=!0,P(()=>{document.activeElement===r&&e.focus()})}}function ge(e){w&&V(e)!==null&&M(e)}let D=!1;function ce(){D||(D=!0,document.addEventListener("reset",e=>{Promise.resolve().then(()=>{var t;if(!e.defaultPrevented)for(const r of e.target.elements)(t=r.__on_r)==null||t.call(r)})},{capture:!0}))}function q(e){var t=j,r=R;m(null),E(null);try{return e()}finally{m(t),E(r)}}function be(e,t,r,i=r){e.addEventListener(t,()=>q(r));const o=e.__on_r;o?e.__on_r=()=>{o(),i(!0)}:e.__on_r=()=>i(!0),ce()}const B=new Set,S=new Set;function le(e,t,r,i={}){function o(a){if(i.capture||y.call(t,a),!a.cancelBubble)return q(()=>r==null?void 0:r.call(this,a))}return e.startsWith("pointer")||e.startsWith("touch")||e==="wheel"?P(()=>{t.addEventListener(e,o,i)}):t.addEventListener(e,o,i),o}function we(e,t,r,i,o){var a={capture:i,passive:o},u=le(e,t,r,a);(t===document.body||t===window||t===document)&&Y(()=>{t.removeEventListener(e,u,a)})}function me(e){for(var t=0;t<e.length;t++)B.add(e[t]);for(var r of S)r(e)}function y(e){var A;var t=this,r=t.ownerDocument,i=e.type,o=((A=e.composedPath)==null?void 0:A.call(e))||[],a=o[0]||e.target,u=0,_=e.__root;if(_){var f=o.indexOf(_);if(f!==-1&&(t===document||t===window)){e.__root=t;return}var p=o.indexOf(t);if(p===-1)return;f<=p&&(u=f)}if(a=o[u]||e.target,a!==t){$(e,"currentTarget",{configurable:!0,get(){return a||r}});var T=j,c=R;m(null),E(null);try{for(var n,s=[];a!==null;){var l=a.assignedSlot||a.parentNode||a.host||null;try{var d=a["__"+i];if(d!=null&&(!a.disabled||e.target===a))if(x(d)){var[H,...W]=d;H.apply(a,[e,...W])}else d.call(a,e)}catch(g){n?s.push(g):n=g}if(e.cancelBubble||l===t||l===null)break;a=l}if(n){for(let g of s)queueMicrotask(()=>{throw g});throw n}}finally{e.__root=t,delete e.currentTarget,m(T),E(c)}}}let O=!0;function Ee(e,t){var r=t==null?"":typeof t=="object"?t+"":t;r!==(e.__t??(e.__t=e.nodeValue))&&(e.__t=r,e.nodeValue=r+"")}function fe(e,t){return C(e,t)}function Te(e,t){L(),t.intro=t.intro??!1;const r=t.target,i=w,o=h;try{for(var a=V(r);a&&(a.nodeType!==8||a.data!==z);)a=F(a);if(!a)throw k;b(!0),I(a),G();const u=C(e,{...t,anchor:a});if(h===null||h.nodeType!==8||h.data!==J)throw U(),k;return b(!1),u}catch(u){if(u===k)return t.recover===!1&&K(),L(),M(r),b(!1),fe(e,t);throw u}finally{b(i),I(o)}}const v=new Map;function C(e,{target:t,anchor:r,props:i={},events:o,context:a,intro:u=!0}){L();var _=new Set,f=c=>{for(var n=0;n<c.length;n++){var s=c[n];if(!_.has(s)){_.add(s);var l=ue(s);t.addEventListener(s,y,{passive:l});var d=v.get(s);d===void 0?(document.addEventListener(s,y,{passive:l}),v.set(s,1)):v.set(s,d+1)}}};f(Q(B)),S.add(f);var p=void 0,T=X(()=>{var c=r??t.appendChild(Z());return ee(()=>{if(a){te({});var n=re;n.c=a}o&&(i.$$events=o),w&&oe(c,null),O=u,p=e(c,i)||{},O=!0,w&&(R.nodes_end=h),a&&ae()}),()=>{var l;for(var n of _){t.removeEventListener(n,y);var s=v.get(n);--s===0?(document.removeEventListener(n,y),v.delete(n)):v.set(n,s)}S.delete(f),c!==r&&((l=c.parentNode)==null||l.removeChild(c))}});return N.set(p,T),p}let N=new WeakMap;function ke(e,t){const r=N.get(e);return r?(N.delete(e),r(t)):Promise.resolve()}export{ye as a,ce as b,le as c,me as d,we as e,ve as f,O as g,Te as h,pe as i,be as l,fe as m,he as n,ge as r,Ee as s,ke as u,q as w};
