var cn=Array.isArray,vn=Array.prototype.indexOf,Jn=Array.from,Wn=Object.defineProperty,K=Object.getOwnPropertyDescriptor,hn=Object.getOwnPropertyDescriptors,pn=Object.prototype,dn=Array.prototype,Ft=Object.getPrototypeOf,Ot=Object.isExtensible;function Xn(t){return typeof t=="function"}const Qn=()=>{};function te(t){return t()}function Lt(t){for(var e=0;e<t.length;e++)t[e]()}const b=2,Mt=4,ut=8,mt=16,k=32,U=64,et=128,T=256,rt=512,y=1024,D=2048,C=4096,H=8192,ot=16384,wn=32768,qt=65536,ne=1<<17,yn=1<<19,jt=1<<20,yt=1<<21,P=Symbol("$state"),ee=Symbol("legacy props"),re=Symbol("");function Yt(t){return t===this.v}function En(t,e){return t!=t?e==e:t!==e||t!==null&&typeof t=="object"||typeof t=="function"}function Ht(t){return!En(t,this.v)}function gn(t){throw new Error("https://svelte.dev/e/effect_in_teardown")}function Tn(){throw new Error("https://svelte.dev/e/effect_in_unowned_derived")}function mn(t){throw new Error("https://svelte.dev/e/effect_orphan")}function An(){throw new Error("https://svelte.dev/e/effect_update_depth_exceeded")}function ae(){throw new Error("https://svelte.dev/e/hydration_failed")}function le(t){throw new Error("https://svelte.dev/e/props_invalid_value")}function xn(){throw new Error("https://svelte.dev/e/state_descriptors_fixed")}function bn(){throw new Error("https://svelte.dev/e/state_prototype_fixed")}function In(){throw new Error("https://svelte.dev/e/state_unsafe_mutation")}let _t=!1;function se(){_t=!0}const fe=1,ie=2,ue=4,oe=8,_e=16,ce=1,ve=2,he=4,pe=8,de=16,we=4,ye=1,Ee=2,Rn="[",On="[!",Dn="]",Bt={},E=Symbol(),ge="http://www.w3.org/1999/xhtml";let p=null;function Dt(t){p=t}function Te(t,e=!1,n){var r=p={p,c:null,d:!1,e:null,m:!1,s:t,x:null,l:null};_t&&!e&&(p.l={s:null,u:null,r1:[],r2:xt(!1)}),Pn(()=>{r.d=!0})}function me(t){const e=p;if(e!==null){t!==void 0&&(e.x=t);const u=e.e;if(u!==null){var n=h,r=v;e.e=null;try{for(var a=0;a<u.length;a++){var l=u[a];st(l.effect),B(l.reaction),Wt(l.fn)}}finally{st(n),B(r)}}p=e.p,e.m=!0}return t||{}}function ct(){return!_t||p!==null&&p.l===null}function j(t){if(typeof t!="object"||t===null||P in t)return t;const e=Ft(t);if(e!==pn&&e!==dn)return t;var n=new Map,r=cn(t),a=N(0),l=v,u=i=>{var s=v;B(l);var f=i();return B(s),f};return r&&n.set("length",N(t.length)),new Proxy(t,{defineProperty(i,s,f){(!("value"in f)||f.configurable===!1||f.enumerable===!1||f.writable===!1)&&xn();var _=n.get(s);return _===void 0?(_=u(()=>N(f.value)),n.set(s,_)):O(_,u(()=>j(f.value))),!0},deleteProperty(i,s){var f=n.get(s);if(f===void 0)s in i&&(n.set(s,u(()=>N(E))),wt(a));else{if(r&&typeof s=="string"){var _=n.get("length"),o=Number(s);Number.isInteger(o)&&o<_.v&&O(_,o)}O(f,E),wt(a)}return!0},get(i,s,f){var x;if(s===P)return t;var _=n.get(s),o=s in i;if(_===void 0&&(!o||(x=K(i,s))!=null&&x.writable)&&(_=u(()=>N(j(o?i[s]:E))),n.set(s,_)),_!==void 0){var c=Y(_);return c===E?void 0:c}return Reflect.get(i,s,f)},getOwnPropertyDescriptor(i,s){var f=Reflect.getOwnPropertyDescriptor(i,s);if(f&&"value"in f){var _=n.get(s);_&&(f.value=Y(_))}else if(f===void 0){var o=n.get(s),c=o==null?void 0:o.v;if(o!==void 0&&c!==E)return{enumerable:!0,configurable:!0,value:c,writable:!0}}return f},has(i,s){var c;if(s===P)return!0;var f=n.get(s),_=f!==void 0&&f.v!==E||Reflect.has(i,s);if(f!==void 0||h!==null&&(!_||(c=K(i,s))!=null&&c.writable)){f===void 0&&(f=u(()=>N(_?j(i[s]):E)),n.set(s,f));var o=Y(f);if(o===E)return!1}return _},set(i,s,f,_){var Rt;var o=n.get(s),c=s in i;if(r&&s==="length")for(var x=f;x<o.v;x+=1){var Q=n.get(x+"");Q!==void 0?O(Q,E):x in i&&(Q=u(()=>N(E)),n.set(x+"",Q))}o===void 0?(!c||(Rt=K(i,s))!=null&&Rt.writable)&&(o=u(()=>N(void 0)),O(o,u(()=>j(f))),n.set(s,o)):(c=o.v!==E,O(o,u(()=>j(f))));var tt=Reflect.getOwnPropertyDescriptor(i,s);if(tt!=null&&tt.set&&tt.set.call(_,f),!c){if(r&&typeof s=="string"){var It=n.get("length"),dt=Number(s);Number.isInteger(dt)&&dt>=It.v&&O(It,dt+1)}wt(a)}return!0},ownKeys(i){Y(a);var s=Reflect.ownKeys(i).filter(o=>{var c=n.get(o);return c===void 0||c.v!==E});for(var[f,_]of n)_.v!==E&&!(f in i)&&s.push(f);return s},setPrototypeOf(){bn()}})}function wt(t,e=1){O(t,t.v+e)}function kt(t){try{if(t!==null&&typeof t=="object"&&P in t)return t[P]}catch{}return t}function Ae(t,e){return Object.is(kt(t),kt(e))}function At(t){var e=b|D,n=v!==null&&(v.f&b)!==0?v:null;return h===null||n!==null&&(n.f&T)!==0?e|=T:h.f|=jt,{ctx:p,deps:null,effects:null,equals:Yt,f:e,fn:t,reactions:null,rv:0,v:null,wv:0,parent:n??h}}function xe(t){const e=At(t);return ln(e),e}function be(t){const e=At(t);return e.equals=Ht,e}function Ut(t){var e=t.effects;if(e!==null){t.effects=null;for(var n=0;n<e.length;n+=1)M(e[n])}}function kn(t){for(var e=t.parent;e!==null;){if((e.f&b)===0)return e;e=e.parent}return null}function Vt(t){var e,n=h;st(kn(t));try{Ut(t),e=on(t)}finally{st(n)}return e}function Gt(t){var e=Vt(t),n=(S||(t.f&T)!==0)&&t.deps!==null?C:y;A(t,n),t.equals(e)||(t.v=e,t.wv=fn())}const $=new Map;function xt(t,e){var n={f:0,v:t,reactions:null,equals:Yt,rv:0,wv:0};return n}function N(t,e){const n=xt(t);return ln(n),n}function Ie(t,e=!1){var r;const n=xt(t);return e||(n.equals=Ht),_t&&p!==null&&p.l!==null&&((r=p.l).s??(r.s=[])).push(n),n}function O(t,e,n=!1){v!==null&&!R&&ct()&&(v.f&(b|mt))!==0&&!(w!=null&&w.includes(t))&&In();let r=n?j(e):e;return Nn(t,r)}function Nn(t,e){if(!t.equals(e)){var n=t.v;X?$.set(t,e):$.set(t,n),t.v=e,(t.f&b)!==0&&((t.f&D)!==0&&Vt(t),A(t,(t.f&T)===0?y:C)),t.wv=fn(),Kt(t,D),ct()&&h!==null&&(h.f&y)!==0&&(h.f&(k|U))===0&&(m===null?Hn([t]):m.push(t))}return e}function Kt(t,e){var n=t.reactions;if(n!==null)for(var r=ct(),a=n.length,l=0;l<a;l++){var u=n[l],i=u.f;(i&D)===0&&(!r&&u===h||(A(u,e),(i&(y|T))!==0&&((i&b)!==0?Kt(u,C):pt(u))))}}function Zt(t){console.warn("https://svelte.dev/e/hydration_mismatch")}let L=!1;function Re(t){L=t}let I;function z(t){if(t===null)throw Zt(),Bt;return I=t}function Oe(){return z(q(I))}function De(t){if(L){if(q(I)!==null)throw Zt(),Bt;I=t}}function ke(t=1){if(L){for(var e=t,n=I;e--;)n=q(n);I=n}}function Ne(){for(var t=0,e=I;;){if(e.nodeType===8){var n=e.data;if(n===Dn){if(t===0)return e;t-=1}else(n===Rn||n===On)&&(t+=1)}var r=q(e);e.remove(),e=r}}var Nt,Sn,$t,zt;function Se(){if(Nt===void 0){Nt=window,Sn=/Firefox/.test(navigator.userAgent);var t=Element.prototype,e=Node.prototype,n=Text.prototype;$t=K(e,"firstChild").get,zt=K(e,"nextSibling").get,Ot(t)&&(t.__click=void 0,t.__className=void 0,t.__attributes=null,t.__style=void 0,t.__e=void 0),Ot(n)&&(n.__t=void 0)}}function Et(t=""){return document.createTextNode(t)}function gt(t){return $t.call(t)}function q(t){return zt.call(t)}function Ce(t,e){if(!L)return gt(t);var n=gt(I);if(n===null)n=I.appendChild(Et());else if(e&&n.nodeType!==3){var r=Et();return n==null||n.before(r),z(r),r}return z(n),n}function Pe(t,e){if(!L){var n=gt(t);return n instanceof Comment&&n.data===""?q(n):n}return I}function Fe(t,e=1,n=!1){let r=L?I:t;for(var a;e--;)a=r,r=q(r);if(!L)return r;var l=r==null?void 0:r.nodeType;if(n&&l!==3){var u=Et();return r===null?a==null||a.after(u):r.before(u),z(u),u}return z(r),r}function Le(t){t.textContent=""}function Jt(t){h===null&&v===null&&mn(),v!==null&&(v.f&T)!==0&&h===null&&Tn(),X&&gn()}function Cn(t,e){var n=e.last;n===null?e.last=e.first=t:(n.next=t,t.prev=n,e.last=t)}function V(t,e,n,r=!0){var a=h,l={ctx:p,deps:null,nodes_start:null,nodes_end:null,f:t|D,first:null,fn:e,last:null,next:null,parent:a,prev:null,teardown:null,transitions:null,wv:0};if(n)try{ht(l),l.f|=wn}catch(s){throw M(l),s}else e!==null&&pt(l);var u=n&&l.deps===null&&l.first===null&&l.nodes_start===null&&l.teardown===null&&(l.f&(jt|et))===0;if(!u&&r&&(a!==null&&Cn(l,a),v!==null&&(v.f&b)!==0)){var i=v;(i.effects??(i.effects=[])).push(l)}return l}function Pn(t){const e=V(ut,null,!1);return A(e,y),e.teardown=t,e}function Me(t){Jt();var e=h!==null&&(h.f&k)!==0&&p!==null&&!p.m;if(e){var n=p;(n.e??(n.e=[])).push({fn:t,effect:h,reaction:v})}else{var r=Wt(t);return r}}function qe(t){return Jt(),bt(t)}function je(t){const e=V(U,t,!0);return(n={})=>new Promise(r=>{n.outro?qn(e,()=>{M(e),r(void 0)}):(M(e),r(void 0))})}function Wt(t){return V(Mt,t,!1)}function Ye(t,e){var n=p,r={effect:null,ran:!1};n.l.r1.push(r),r.effect=bt(()=>{t(),!r.ran&&(r.ran=!0,O(n.l.r2,!0),$n(e))})}function He(){var t=p;bt(()=>{if(Y(t.l.r2)){for(var e of t.l.r1){var n=e.effect;(n.f&y)!==0&&A(n,C),G(n)&&ht(n),e.ran=!1}t.l.r2.v=!1}})}function bt(t){return V(ut,t,!0)}function Be(t,e=[],n=At){const r=e.map(n);return Fn(()=>t(...r.map(Y)))}function Fn(t,e=0){return V(ut|mt|e,t,!0)}function Ue(t,e=!0){return V(ut|k,t,!0,e)}function Xt(t){var e=t.teardown;if(e!==null){const n=X,r=v;Ct(!0),B(null);try{e.call(null)}finally{Ct(n),B(r)}}}function Qt(t,e=!1){var n=t.first;for(t.first=t.last=null;n!==null;){var r=n.next;(n.f&U)!==0?n.parent=null:M(n,e),n=r}}function Ln(t){for(var e=t.first;e!==null;){var n=e.next;(e.f&k)===0&&M(e),e=n}}function M(t,e=!0){var n=!1;(e||(t.f&yn)!==0)&&t.nodes_start!==null&&(Mn(t.nodes_start,t.nodes_end),n=!0),Qt(t,e&&!n),it(t,0),A(t,ot);var r=t.transitions;if(r!==null)for(const l of r)l.stop();Xt(t);var a=t.parent;a!==null&&a.first!==null&&tn(t),t.next=t.prev=t.teardown=t.ctx=t.deps=t.fn=t.nodes_start=t.nodes_end=null}function Mn(t,e){for(;t!==null;){var n=t===e?null:q(t);t.remove(),t=n}}function tn(t){var e=t.parent,n=t.prev,r=t.next;n!==null&&(n.next=r),r!==null&&(r.prev=n),e!==null&&(e.first===t&&(e.first=r),e.last===t&&(e.last=n))}function qn(t,e){var n=[];nn(t,n,!0),jn(n,()=>{M(t),e&&e()})}function jn(t,e){var n=t.length;if(n>0){var r=()=>--n||e();for(var a of t)a.out(r)}else e()}function nn(t,e,n){if((t.f&H)===0){if(t.f^=H,t.transitions!==null)for(const u of t.transitions)(u.is_global||n)&&e.push(u);for(var r=t.first;r!==null;){var a=r.next,l=(r.f&qt)!==0||(r.f&k)!==0;nn(r,e,l?n:!1),r=a}}}function Ve(t){en(t,!0)}function en(t,e){if((t.f&H)!==0){t.f^=H,(t.f&y)===0&&(t.f^=y),G(t)&&(A(t,D),pt(t));for(var n=t.first;n!==null;){var r=n.next,a=(n.f&qt)!==0||(n.f&k)!==0;en(n,a?e:!1),n=r}if(t.transitions!==null)for(const l of t.transitions)(l.is_global||e)&&l.in()}}const Yn=typeof requestIdleCallback>"u"?t=>setTimeout(t,1):requestIdleCallback;let J=[],W=[];function rn(){var t=J;J=[],Lt(t)}function an(){var t=W;W=[],Lt(t)}function Ge(t){J.length===0&&queueMicrotask(rn),J.push(t)}function Ke(t){W.length===0&&Yn(an),W.push(t)}function St(){J.length>0&&rn(),W.length>0&&an()}let nt=!1,at=!1,lt=null,F=!1,X=!1;function Ct(t){X=t}let Z=[];let v=null,R=!1;function B(t){v=t}let h=null;function st(t){h=t}let w=null;function ln(t){v!==null&&v.f&yt&&(w===null?w=[t]:w.push(t))}let d=null,g=0,m=null;function Hn(t){m=t}let sn=1,ft=0,S=!1;function fn(){return++sn}function G(t){var o;var e=t.f;if((e&D)!==0)return!0;if((e&C)!==0){var n=t.deps,r=(e&T)!==0;if(n!==null){var a,l,u=(e&rt)!==0,i=r&&h!==null&&!S,s=n.length;if(u||i){var f=t,_=f.parent;for(a=0;a<s;a++)l=n[a],(u||!((o=l==null?void 0:l.reactions)!=null&&o.includes(f)))&&(l.reactions??(l.reactions=[])).push(f);u&&(f.f^=rt),i&&_!==null&&(_.f&T)===0&&(f.f^=T)}for(a=0;a<s;a++)if(l=n[a],G(l)&&Gt(l),l.wv>t.wv)return!0}(!r||h!==null&&!S)&&A(t,y)}return!1}function Bn(t,e){for(var n=e;n!==null;){if((n.f&et)!==0)try{n.fn(t);return}catch{n.f^=et}n=n.parent}throw nt=!1,t}function Pt(t){return(t.f&ot)===0&&(t.parent===null||(t.parent.f&et)===0)}function vt(t,e,n,r){if(nt){if(n===null&&(nt=!1),Pt(e))throw t;return}if(n!==null&&(nt=!0),Bn(t,e),Pt(e))throw t}function un(t,e,n=!0){var r=t.reactions;if(r!==null)for(var a=0;a<r.length;a++){var l=r[a];w!=null&&w.includes(t)||((l.f&b)!==0?un(l,e,!1):e===l&&(n?A(l,D):(l.f&y)!==0&&A(l,C),pt(l)))}}function on(t){var x;var e=d,n=g,r=m,a=v,l=S,u=w,i=p,s=R,f=t.f;d=null,g=0,m=null,S=(f&T)!==0&&(R||!F||v===null),v=(f&(k|U))===0?t:null,w=null,Dt(t.ctx),R=!1,ft++,t.f|=yt;try{var _=(0,t.fn)(),o=t.deps;if(d!==null){var c;if(it(t,g),o!==null&&g>0)for(o.length=g+d.length,c=0;c<d.length;c++)o[g+c]=d[c];else t.deps=o=d;if(!S)for(c=g;c<o.length;c++)((x=o[c]).reactions??(x.reactions=[])).push(t)}else o!==null&&g<o.length&&(it(t,g),o.length=g);if(ct()&&m!==null&&!R&&o!==null&&(t.f&(b|C|D))===0)for(c=0;c<m.length;c++)un(m[c],t);return a!==null&&a!==t&&(ft++,m!==null&&(r===null?r=m:r.push(...m))),_}finally{d=e,g=n,m=r,v=a,S=l,w=u,Dt(i),R=s,t.f^=yt}}function Un(t,e){let n=e.reactions;if(n!==null){var r=vn.call(n,t);if(r!==-1){var a=n.length-1;a===0?n=e.reactions=null:(n[r]=n[a],n.pop())}}n===null&&(e.f&b)!==0&&(d===null||!d.includes(e))&&(A(e,C),(e.f&(T|rt))===0&&(e.f^=rt),Ut(e),it(e,0))}function it(t,e){var n=t.deps;if(n!==null)for(var r=e;r<n.length;r++)Un(t,n[r])}function ht(t){var e=t.f;if((e&ot)===0){A(t,y);var n=h,r=p,a=F;h=t,F=!0;try{(e&mt)!==0?Ln(t):Qt(t),Xt(t);var l=on(t);t.teardown=typeof l=="function"?l:null,t.wv=sn;var u=t.deps,i}catch(s){vt(s,t,n,r||t.ctx)}finally{F=a,h=n}}}function Vn(){try{An()}catch(t){if(lt!==null)vt(t,lt,null);else throw t}}function _n(){var t=F;try{var e=0;for(F=!0;Z.length>0;){e++>1e3&&Vn();var n=Z,r=n.length;Z=[];for(var a=0;a<r;a++){var l=Kn(n[a]);Gn(l)}$.clear()}}finally{at=!1,F=t,lt=null}}function Gn(t){var e=t.length;if(e!==0)for(var n=0;n<e;n++){var r=t[n];if((r.f&(ot|H))===0)try{G(r)&&(ht(r),r.deps===null&&r.first===null&&r.nodes_start===null&&(r.teardown===null?tn(r):r.fn=null))}catch(a){vt(a,r,null,r.ctx)}}}function pt(t){at||(at=!0,queueMicrotask(_n));for(var e=lt=t;e.parent!==null;){e=e.parent;var n=e.f;if((n&(U|k))!==0){if((n&y)===0)return;e.f^=y}}Z.push(e)}function Kn(t){for(var e=[],n=t;n!==null;){var r=n.f,a=(r&(k|U))!==0,l=a&&(r&y)!==0;if(!l&&(r&H)===0){if((r&Mt)!==0)e.push(n);else if(a)n.f^=y;else try{G(n)&&ht(n)}catch(s){vt(s,n,null,n.ctx)}var u=n.first;if(u!==null){n=u;continue}}var i=n.parent;for(n=n.next;n===null&&i!==null;)n=i.next,i=i.parent}return e}function Zn(t){var e;for(St();Z.length>0;)at=!0,_n(),St();return e}async function Ze(){await Promise.resolve(),Zn()}function Y(t){var e=t.f,n=(e&b)!==0;if(v!==null&&!R){if(!(w!=null&&w.includes(t))){var r=v.deps;t.rv<ft&&(t.rv=ft,d===null&&r!==null&&r[g]===t?g++:d===null?d=[t]:(!S||!d.includes(t))&&d.push(t))}}else if(n&&t.deps===null&&t.effects===null){var a=t,l=a.parent;l!==null&&(l.f&T)===0&&(a.f^=T)}return n&&(a=t,G(a)&&Gt(a)),X&&$.has(t)?$.get(t):t.v}function $n(t){var e=R;try{return R=!0,t()}finally{R=e}}const zn=-7169;function A(t,e){t.f=t.f&zn|e}function $e(t){if(!(typeof t!="object"||!t||t instanceof EventTarget)){if(P in t)Tt(t);else if(!Array.isArray(t))for(let e in t){const n=t[e];typeof n=="object"&&n&&P in n&&Tt(n)}}}function Tt(t,e=new Set){if(typeof t=="object"&&t!==null&&!(t instanceof EventTarget)&&!e.has(t)){e.add(t),t instanceof Date&&t.getTime();for(let r in t)try{Tt(t[r],e)}catch{}const n=Ft(t);if(n!==Object.prototype&&n!==Array.prototype&&n!==Map.prototype&&n!==Set.prototype&&n!==Date.prototype){const r=hn(n);for(let a in r){const l=r[a].get;if(l)try{l.call(t)}catch{}}}}}export{pe as $,bt as A,L as B,Fn as C,Oe as D,qt as E,On as F,Ne as G,Rn as H,z as I,Re as J,Ve as K,Ue as L,qn as M,I as N,Ze as O,N as P,xe as Q,Ge as R,P as S,K as T,E as U,le as V,ne as W,be as X,he as Y,Ht as Z,j as _,me as a,ee as a0,de as a1,_t as a2,ve as a3,ce as a4,ge as a5,Ft as a6,re as a7,Ke as a8,hn as a9,M as aA,fe as aB,_e as aC,ue as aD,oe as aE,mt as aF,wn as aG,we as aH,Xn as aI,Zn as aJ,Ye as aK,He as aL,En as aa,gt as ab,Sn as ac,Et as ad,ye as ae,Ee as af,h as ag,Le as ah,B as ai,st as aj,v as ak,cn as al,Se as am,q as an,Bt as ao,Dn as ap,Zt as aq,ae as ar,Jn as as,je as at,H as au,Nn as av,xt as aw,ie as ax,nn as ay,jn as az,p as b,Ce as c,Me as d,Lt as e,Pe as f,$n as g,te as h,Y as i,$e as j,At as k,Wt as l,Ae as m,ke as n,Pn as o,Te as p,Wn as q,De as r,Fe as s,Be as t,qe as u,Qn as v,Ie as w,O as x,se as y,ct as z};
