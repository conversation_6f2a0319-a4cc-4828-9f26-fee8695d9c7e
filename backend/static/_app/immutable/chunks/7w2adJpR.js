import{s as n,p as t,n as r}from"./DgxGL_Xb.js";const u={get data(){return t.data},get error(){return t.error},get form(){return t.form},get params(){return t.params},get route(){return t.route},get state(){return t.state},get status(){return t.status},get url(){return t.url}},e={get from(){return r.current?r.current.from:null},get to(){return r.current?r.current.to:null},get type(){return r.current?r.current.type:null},get willUnload(){return r.current?r.current.willUnload:null},get delta(){return r.current?r.current.delta:null},get complete(){return r.current?r.current.complete:null}};Object.defineProperty(e,"current",{get(){throw new Error("Replace navigating.current.<prop> with navigating.<prop>")}});n.updated.check;const g=u,o=e;export{o as n,g as p};
