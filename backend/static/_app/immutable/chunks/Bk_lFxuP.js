import{z as k,g as n,A as s,B as u}from"./B9CKrN7X.js";import{l as _}from"./Cc0s-Eqn.js";function t(e,a,v=a){var c=k();_(e,"input",l=>{var r=l?e.defaultValue:e.value;if(r=f(e)?d(r):r,v(r),c&&r!==(r=a())){var h=e.selectionStart,o=e.selectionEnd;e.value=r??"",o!==null&&(e.selectionStart=h,e.selectionEnd=Math.min(o,e.value.length))}}),(u&&e.defaultValue!==e.value||n(a)==null&&e.value)&&v(f(e)?d(e.value):e.value),s(()=>{var l=a();f(e)&&l===d(e.value)||e.type==="date"&&!l&&!e.value||l!==e.value&&(e.value=l??"")})}function y(e,a,v=a){_(e,"change",c=>{var l=c?e.defaultChecked:e.checked;v(l)}),(u&&e.defaultChecked!==e.checked||n(a)==null)&&v(e.checked),s(()=>{var c=a();e.checked=!!c})}function f(e){var a=e.type;return a==="number"||a==="range"}function d(e){return e===""?null:+e}export{y as a,t as b};
