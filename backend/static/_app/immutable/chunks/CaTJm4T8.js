import{w as a,d as l}from"./D6jnoBFN.js";const t="app_auth_state";function u(){const e=localStorage.getItem(t);if(e)try{const r=JSON.parse(e);if(r&&typeof r.accessToken=="string"||r.accessToken===null)return r}catch(r){console.error("Error parsing stored auth state:",r),localStorage.removeItem(t)}return{user:null,accessToken:null,refreshToken:null}}const{subscribe:n,set:c,update:o}=a(u());n(e=>{localStorage.setItem(t,JSON.stringify(e))});function i(e,r){o(s=>({...s,user:r||s.user,accessToken:e.access_token,refreshToken:e.refresh_token}))}function f(){c({user:null,accessToken:null,refreshToken:null}),localStorage.removeItem(t)}function T(e){o(r=>({...r,user:e}))}function k(e){o(r=>{const s={...r};return e.accessToken!==void 0&&(s.accessToken=e.accessToken),e.refreshToken!==void 0&&(s.refreshToken=e.refreshToken),s})}const S=l({subscribe:n},e=>!!e.accessToken),g={subscribe:n,set:c,update:o,login:i,logout:f,setUserProfile:T,setTokens:k};export{g as a,S as i};
