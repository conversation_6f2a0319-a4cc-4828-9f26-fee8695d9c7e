import{C as p,B as u,D as g,E as D,H as S,F as h,G as F,I as H,J as T,K as b,L as v,M as A,U as L,N as k}from"./B9CKrN7X.js";function O(E,N,[t,s]=[0,0]){u&&t===0&&g();var a=E,f=null,e=null,i=L,m=t>0?D:0,c=!1;const R=(n,l=!0)=>{c=!0,o(l,n)},o=(n,l)=>{if(i===(i=n))return;let I=!1;if(u&&s!==-1){if(t===0){const r=a.data;r===S?s=0:r===h?s=1/0:(s=parseInt(r.substring(1)),s!==s&&(s=i?1/0:-1))}const _=s>t;!!i===_&&(a=F(),H(a),T(!1),I=!0,s=-1)}i?(f?b(f):l&&(f=v(()=>l(a))),e&&A(e,()=>{e=null})):(e?b(e):l&&(e=v(()=>l(a,[t+1,s]))),f&&A(f,()=>{f=null})),I&&T(!0)};p(()=>{c=!1,N(R),c||o(null,null)},m),u&&(a=k)}export{O as i};
