import{c as W,a as h,t as k}from"./CEORzCZH.js";import{p as G,d as I,O as C,i as b,P as J,f as N,a as Q,c as u,s as v,r as m,x as R,t as T}from"./B9CKrN7X.js";import{d as U,s as V}from"./Cc0s-Eqn.js";import{i as L}from"./C9zVtFY0.js";import{s as M,t as O,f as X,a as Y}from"./D8moldTO.js";import{s as B}from"./DUXSSeDn.js";import{s as Z}from"./BIuBsydj.js";import{p,b as $}from"./B16-Q6Ob.js";import{o as ee,a as te}from"./D6jnoBFN.js";var oe=(o,a)=>{(o.key==="Enter"||o.key==="Space")&&o.target===o.currentTarget&&(a(o),o.preventDefault())},ae=k('<h2 id="modal-title" class="text-xl font-semibold text-gray-900 dark:text-white m-0"> </h2>'),re=k('<p class="text-gray-700 dark:text-gray-300">This is the modal body. Pass content to override this.</p>'),le=k('<div class="fixed inset-0 bg-black/60 flex justify-center items-center p-4 z-modal" role="dialog" tabindex="-1" aria-modal="true"><div role="document" tabindex="-1"><header class="flex justify-between items-center p-4 border-b border-gray-200 dark:border-gray-700 flex-shrink-0"><!> <button class="bg-transparent border-none text-3xl font-light text-gray-500 dark:text-gray-400 cursor-pointer p-1 leading-none opacity-70 hover:opacity-100 transition-opacity" aria-label="Close modal">&times;</button></header> <main class="p-6 overflow-y-auto flex-grow"><!></main> <!></div></div>');function pe(o,a){G(a,!0);let s=p(a,"isOpen",3,!1),F=p(a,"closeOnBackdropClick",3,!0),g=p(a,"title",3,null),S=p(a,"modalWidth",3,"max-w-lg"),D=p(a,"close",3,()=>{}),d=J(null);function x(){D()()}function w(e){e.currentTarget&&e.target===e.currentTarget&&F()&&x()}function _(e){if(e.key==="Escape"&&s()&&x(),e.key==="Tab"&&s()&&b(d)){const t=Array.from(b(d).querySelectorAll('a[href]:not([disabled]), button:not([disabled]), input:not([disabled]), textarea:not([disabled]), select:not([disabled]), details:not([disabled]), [tabindex]:not([tabindex="-1"])')).filter(c=>c instanceof HTMLElement&&c.offsetParent!==null);if(t.length===0)return;const l=t[0],i=t[t.length-1];e.shiftKey?document.activeElement===l&&(i.focus(),e.preventDefault()):document.activeElement===i&&(l.focus(),e.preventDefault())}}let r=null;ee(()=>{window.addEventListener("keydown",_)}),te(()=>{window.removeEventListener("keydown",_),typeof document<"u"&&document.body.classList.remove("overflow-hidden-modal"),r&&typeof r.focus=="function"&&r.focus()}),I(()=>{s()&&typeof document<"u"?(document.body.classList.add("overflow-hidden-modal"),document.activeElement instanceof HTMLElement&&(r=document.activeElement),C().then(()=>{if(b(d)){const e=b(d).querySelector('a[href]:not([disabled]), button:not([disabled]), input:not([disabled]), textarea:not([disabled]), select:not([disabled]), details:not([disabled]), [tabindex]:not([tabindex="-1"])');e&&e.focus()}})):!s()&&typeof document<"u"&&(document.body.classList.remove("overflow-hidden-modal"),r&&typeof r.focus=="function"&&C().then(()=>r==null?void 0:r.focus()))});var E=W(),j=N(E);{var P=e=>{var t=le();t.__click=w,t.__keydown=[oe,w];var l=u(t),i=u(l),c=u(i);{var q=n=>{var f=ae(),K=u(f,!0);m(f),T(()=>V(K,g())),h(n,f)};L(c,n=>{g()&&n(q)})}var z=v(c,2);z.__click=x,m(i);var y=v(i,2),A=u(y);M(A,a,"default",{},n=>{var f=re();h(n,f)}),m(y);var H=v(y,2);M(H,a,"footer",{},null),m(l),$(l,n=>R(d,n),()=>b(d)),m(t),T(()=>{B(t,"aria-labelledby",g()?"modal-title":void 0),Z(l,1,`bg-white dark:bg-gray-800 rounded-lg shadow-xl flex flex-col w-full max-h-[90vh] overflow-hidden ${S()??""}`),B(l,"aria-labelledby",g()?"modal-title":void 0)}),O(3,l,()=>X,()=>({y:-30,duration:300})),O(3,t,()=>Y,()=>({duration:200})),h(e,t)};L(j,e=>{s()&&e(P)})}h(o,E),Q()}U(["click","keydown"]);const ge="max-w-7xl mx-auto px-4 py-4",he="rounded-lg shadow-md border overflow-hidden bg-white dark:bg-gray-800",xe={plan:{border:"border-green-200 dark:border-green-800",text:"text-green-900 dark:text-green-100",icon:"text-green-500",hover:"hover:bg-green-100 dark:hover:bg-green-800/50",scrollbar:"bg-green-500"},doing:{border:"border-blue-200 dark:border-blue-800",text:"text-blue-900 dark:text-blue-100",icon:"text-blue-500",hover:"hover:bg-blue-100 dark:hover:bg-blue-800/50",scrollbar:"bg-blue-500"},done:{border:"border-purple-200 dark:border-purple-800",text:"text-purple-900 dark:text-purple-100",icon:"text-purple-500",hover:"hover:bg-purple-100 dark:hover:bg-purple-800/50",scrollbar:"bg-purple-500"}},ye={twoColumnOneThree:"grid grid-cols-1 lg:grid-cols-4 gap-6"},ve={oneFourth:"lg:col-span-1",threeFourths:"lg:col-span-3"},ke={h1:"text-3xl font-bold mb-4 flex items-center",h2:"text-xl font-semibold mb-4 flex items-center",h3:"text-lg font-semibold mb-2"},we={container:"relative overflow-hidden",indicator:"absolute top-0 bottom-0 w-1 opacity-50 z-10"};function _e(...o){return o.filter(Boolean).join(" ")}export{pe as M,ve as a,xe as b,_e as c,he as d,ke as h,ye as l,ge as p,we as s};
