import{l as Y,A as m,g as x,R as B,S as w,T as M,V as N,W as U,k as b,X as $,i as d,Y as y,Z as C,_ as G,x as K,w as V,$ as Z,a0 as z,a1 as W,a2 as X,a3 as j,a4 as F}from"./B9CKrN7X.js";import{c as H}from"./BIYeehul.js";function L(r,e){return r===e||(r==null?void 0:r[w])===e}function rr(r={},e,i,c){return Y(()=>{var u,n;return m(()=>{u=n,n=[],x(()=>{r!==i(...n)&&(e(r,...n),u&&L(i(...u),r)&&e(null,...u))})}),()=>{B(()=>{n&&L(i(...n),r)&&e(null,...n)})}}),r}const J={get(r,e){if(!r.exclude.includes(e))return r.props[e]},set(r,e){return!1},getOwnPropertyDescriptor(r,e){if(!r.exclude.includes(e)&&e in r.props)return{enumerable:!0,configurable:!0,value:r.props[e]}},has(r,e){return r.exclude.includes(e)?!1:e in r.props},ownKeys(r){return Reflect.ownKeys(r.props).filter(e=>!r.exclude.includes(e))}};function er(r,e,i){return new Proxy({props:r,exclude:e},J)}function g(r){var e;return((e=r.ctx)==null?void 0:e.d)??!1}function ar(r,e,i,c){var O;var u=(i&F)!==0,n=!X||(i&j)!==0,P=(i&Z)!==0,D=(i&W)!==0,h=!1,f;P?[f,h]=H(()=>r[e]):f=r[e];var T=w in r||z in r,v=P&&(((O=M(r,e))==null?void 0:O.set)??(T&&e in r&&(a=>r[e]=a)))||void 0,t=c,S=!0,o=!1,A=()=>(o=!0,S&&(S=!1,D?t=x(c):t=c),t);f===void 0&&c!==void 0&&(v&&n&&N(),f=A(),v&&v(f));var l;if(n)l=()=>{var a=r[e];return a===void 0?A():(S=!0,o=!1,a)};else{var E=(u?b:$)(()=>r[e]);E.f|=U,l=()=>{var a=d(E);return a!==void 0&&(t=void 0),a===void 0?t:a}}if((i&y)===0)return l;if(v){var q=r.$$legacy;return function(a,_){return arguments.length>0?((!n||!_||q||h)&&v(_?l():a),a):l()}}var p=!1,I=V(f),s=b(()=>{var a=l(),_=d(I);return p?(p=!1,_):I.v=a});return P&&d(s),u||(s.equals=C),function(a,_){if(arguments.length>0){const R=_?d(s):n&&P?G(a):a;if(!s.equals(R)){if(p=!0,K(I,R),o&&t!==void 0&&(t=R),g(s))return a;x(()=>d(s))}return a}return g(s)?s.v:d(s)}}export{rr as b,ar as p,er as r};
